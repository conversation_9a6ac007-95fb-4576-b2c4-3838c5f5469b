{"name": "ibom-music-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/react-vertical-timeline-component": "^3.3.6", "date-fns": "^4.1.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-confetti": "^6.4.0", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3", "react-vertical-timeline-component": "^3.5.3", "tone": "^15.0.4"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "prop-types": "^15.8.1", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}