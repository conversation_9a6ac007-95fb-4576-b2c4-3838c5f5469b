import React from 'react';
import { FileText, Music, Image, Video, FileArchive, FolderArchive, FileQuestion } from 'lucide-react';
import { UploadedFile } from '../types';

/**
 * Returns a Lucide icon component based on the file type.
 */
export const getFileTypeIcon = (type: UploadedFile['type']): React.ElementType => {
    switch (type) {
        case 'audio':
            return Music;
        case 'video':
            return Video;
        case 'image':
            return Image;
        case 'document': // Includes .txt, .doc, .pdf for simplicity
            return FileText;
        case 'score': // e.g., .pdf, .musicxml
            return Music; // Reuse Music or specific score icon if available
        case 'midi':
            return FileArchive; // Generic archive icon for MIDI
        case 'other':
        default:
            return FileQuestion; // Icon for unknown/other types
    }
}; 