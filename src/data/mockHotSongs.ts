import { HotSong, User } from '../types';
import { mockUserProfile } from '../pages/Profile'; // Import demo user for creator example
import { mockProjectsWorkspaceData } from './mockProjects'; // Import projects to link creators

// Helper to find creator info from mock projects (can be improved)
const findCreators = (projectId: string): Pick<User, 'id' | 'name' | 'avatar'>[] => {
    const project = mockProjectsWorkspaceData.find(p => p.id === projectId);
    if (project && project.members && project.members.length > 0) { // Check if members array is not empty
        // Return project members as creators
        return project.members;
    }
    // Fallback: Use project creator (if available and has an ID - which it doesn't in current type) or the demo user
    // For simplicity, always fallback to demo user if members are empty
    // In a real app, project.creator should likely have an ID
    return [{
        id: mockUserProfile.id,
        name: mockUserProfile.name,
        avatar: mockUserProfile.avatar
    }];
};

export const mockHotSongsData: HotSong[] = [
    {
        id: 's1',
        title: '夏夜微风',
        artists: ['星辰乐队'], // Display artists
        coverArt: 'https://images.unsplash.com/photo-1487180144351-b8472da7d491?auto=format&fit=crop&w=300&q=80',
        plays: 125000,
        genre: '流行',
        projectId: 'proj1', // Link to project 1
        creators: findCreators('proj1'), // Get creators from project 1 members
        supportCount: 1523,
        likeCount: 128,
    },
    {
        id: 's2',
        title: '城市霓虹',
        artists: ['DJ 暗影', '莉莉'],
        coverArt: 'https://images.unsplash.com/photo-1506157786151-b8491531f063?auto=format&fit=crop&w=300&q=80',
        plays: 98000,
        genre: '电子',
        projectId: 'proj3', // Link to project 3 (example)
        creators: findCreators('proj3'),
        supportCount: 890,
        likeCount: 95,
    },
    {
        id: 's3',
        title: '雨后初晴',
        artists: ['清风'],
        coverArt: 'https://images.unsplash.com/photo-1698306864481-32824ed99904?auto=format&fit=crop&w=300&q=80',
        plays: 85000,
        genre: '民谣',
        projectId: 'proj1', // Link to project 1 (example)
        creators: findCreators('proj1'),
        supportCount: 750,
        likeCount: 72,
    },
    {
        id: 's6',
        title: '街头节奏',
        artists: ['说唱诗人'],
        coverArt: 'https://images.unsplash.com/photo-1516280440614-37939bbacd81?auto=format&fit=crop&w=300&q=80',
        plays: 110000,
        genre: '嘻哈',
        projectId: 'proj2', // Link to project 2
        creators: findCreators('proj2'),
        supportCount: 1134,
        likeCount: 150,
    },
    {
        id: 's8',
        title: '未来之声',
        artists: ['电音先驱', 'AI歌姬'],
        coverArt: 'https://images.unsplash.com/photo-1618005198919-d3d4b5a92ead?auto=format&fit=crop&w=300&q=80',
        plays: 92000,
        genre: '电子',
        projectId: 'proj3',
        creators: findCreators('proj3'),
        supportCount: 910,
        likeCount: 88,
    },
    // Add more mock songs linked to projects...
];

// Simulate API fetching hot songs
export const fetchHotSongs = (): Promise<HotSong[]> => {
    console.log("Fetching hot songs...");
    return new Promise((resolve) => {
        setTimeout(() => {
            // Return a shuffled/sliced version for variety if needed
            resolve([...mockHotSongsData]); // Return a copy
        }, 200); // Simulate network delay
    });
}; 