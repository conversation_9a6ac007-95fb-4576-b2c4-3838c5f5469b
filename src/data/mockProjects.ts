import { ProjectDetailData, UserRole, UploadedFile, DiscussionComment, TimelineEvent, ProjectWorkspaceData, FileFolder, FileComment, UserSummary } from '../types';
// Lucide icons are now imported in the component where they are used (e.g., TimelineView.tsx)
// import { MusicNote, Mic, Edit3, GitMerge, CheckCircle } from 'lucide-react';

// ---------- Mock Timeline Data ----------
const mockTimelineProj1: TimelineEvent[] = [
    { id: 'tl1-1', title: '项目启动与构思', date: '2024-04-01', status: 'completed', description: '确定项目方向和风格。' },
    { id: 'tl1-2', title: '节奏型与和声编写', date: '2024-04-03', status: 'completed', description: '完成基础 Beat 和和声进行。' },
    { id: 'tl1-3', title: '歌词创作', date: '2024-04-06', status: 'completed', description: '完成歌词初稿。' },
    { id: 'tl1-4', title: '旋律创作', date: '进行中', status: 'ongoing', description: '主旋律编写中...' },
    { id: 'tl1-5', title: '歌手招募', date: '待定', status: 'planned', description: '寻找合适的歌手演绎。' },
];

const mockTimelineProj2: TimelineEvent[] = [
    { id: 'tl2-1', title: '选定诗词与立意', date: '2024-03-15', status: 'completed' },
    { id: 'tl2-2', title: '主旋律与编曲', date: '2024-03-25', status: 'completed' },
    { id: 'tl2-3', title: '人声录制', date: '2024-03-30', status: 'completed' },
    { id: 'tl2-4', title: '乐器实录与混音', date: '2024-04-05', status: 'completed' },
    { id: 'tl2-5', title: '项目完成', date: '2024-04-05', status: 'completed' },
];

const mockTimelineProj3: TimelineEvent[] = [
    { id: 'tl3-1', title: '需求发布', date: '2024-04-12', status: 'completed', description: '发布配乐需求和参考片段。' },
    { id: 'tl3-2', title: '招募作曲/编曲', date: '进行中', status: 'ongoing', description: '等待音乐人加入并提交 Demo。' },
    { id: 'tl3-3', title: 'Demo 筛选与确定', date: '待定', status: 'planned' },
    { id: 'tl3-4', title: '最终混音', date: '待定', status: 'planned' },
];

// ---------- Mock Folders ----------
const mockFoldersProj1: FileFolder[] = [
    { id: 'folder1-1', name: '音频 Stems', projectId: 'proj1' },
    { id: 'folder1-2', name: '歌词文档', projectId: 'proj1' },
    { id: 'folder1-3', name: '参考曲目', projectId: 'proj1' },
];

const mockFoldersProj2: FileFolder[] = [
    { id: 'folder2-1', name: '成品区', projectId: 'proj2' },
    { id: 'folder2-2', name: '乐谱', projectId: 'proj2' },
    { id: 'folder2-3', name: '分轨', projectId: 'proj2' },
];

const mockFoldersProj3: FileFolder[] = [
    { id: 'folder3-1', name: '参考资料', projectId: 'proj3' },
    { id: 'folder3-2', name: 'Demo 提交', projectId: 'proj3' },
];

// Combine all mock folders (in real app, fetch based on projectId)
export const mockFolders: FileFolder[] = [...mockFoldersProj1, ...mockFoldersProj2, ...mockFoldersProj3];

// ---------- Mock Comments ----------
const mockComment1: FileComment = {
    id: 'fc1',
    author: { id: 'user2', name: '李四', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=LiSi' },
    timestamp: '2024-04-09T10:00:00Z',
    text: '这个旋律片段感觉不错，主歌部分可以再调整一下吗？'
};
const mockComment2: FileComment = {
    id: 'fc2',
    author: { id: 'user1', name: '张三', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ZhangSan' },
    timestamp: '2024-04-09T11:30:00Z',
    text: '收到！我晚上再改改看。'
};

// Mock previous versions for one file
const mockPrevVersionsF1_3 = [
    {
        uploadedAt: '2024-04-08T14:00:00Z',
        fileUrl: '#version1',
        uploader: { id: 'user1', name: '张三', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ZhangSan' },
    },
    {
        uploadedAt: '2024-04-07T10:30:00Z',
        fileUrl: '#version0',
        uploader: { id: 'user1', name: '张三', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ZhangSan' },
    }
];

// ---------- Mock Project Data (Updated with folderId, comments, and previousVersions) ----------

export const mockProjectsWorkspaceData: ProjectWorkspaceData[] = [
    {
        id: 'proj1',
        name: '夏日清凉音乐节',
        description: '创作一首充满夏日气息的清凉音乐，融合电子和民谣元素。',
        progress: 75,
        status: 'ongoing' as ProjectDetailData['status'],
        members: [
            { id: 'user1', name: '张三', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ZhangSan' },
            { id: 'user2', name: '李四', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=LiSi' },
        ],
        createdAt: '2024-04-01T10:00:00Z',
        updatedAt: '2024-04-08T15:30:00Z',
        tags: ['电子音乐', '民谣', '夏日', '招募歌手'],
        creator: { id: 'user1', name: '张三', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ZhangSan' },
        neededRoles: [
            { role: 'composer' as UserRole, filled: true },
            { role: 'lyricist' as UserRole, filled: true },
            { role: 'singer' as UserRole, filled: false },
            { role: 'arranger' as UserRole, filled: false },
            { role: 'mixer' as UserRole, filled: false },
        ],
        uploadedFiles: [
            { id: 'f_proj1_1', name: '节奏型参考.mp3', type: 'audio' as UploadedFile['type'], uploader: { id: 'user1', name: '张三', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ZhangSan' }, uploadedAt: '2024-04-02T11:00:00Z', fileUrl: '#', folderId: 'folder1-3' },
            { id: 'f_proj1_2', name: '歌词初稿.txt', type: 'document' as UploadedFile['type'], uploader: { id: 'user2', name: '李四', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=LiSi' }, uploadedAt: '2024-04-05T14:20:00Z', fileUrl: '#', folderId: 'folder1-2' },
            {
                id: 'f_proj1_3',
                name: '主旋律片段1.wav',
                type: 'audio' as UploadedFile['type'],
                uploader: { id: 'user1', name: '张三', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ZhangSan' },
                uploadedAt: '2024-04-09T09:15:00Z', // Current version timestamp
                fileUrl: '#',
                folderId: 'folder1-1',
                comments: [mockComment1, mockComment2],
                previousVersions: mockPrevVersionsF1_3 // Add previous versions here
            },
        ],
        discussion: [
            { id: 'd_proj1_1', author: { id: 'user2', name: '李四', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=LiSi' }, content: '歌词初稿写好了，大家看看方向对不对？', timestamp: '2024-04-05T14:25:00Z' },
            { id: 'd_proj1_2', author: { id: 'user1', name: '张三', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ZhangSan' }, content: '感觉不错！很有夏天的味道！我写了一段主旋律，传上来了。', timestamp: '2024-04-09T09:20:00Z' },
        ],
        timeline: mockTimelineProj1,
    },
    {
        id: 'proj2',
        name: '古风音乐创作',
        description: '以古典诗词为灵感，创作一首融合传统与现代元素的音乐作品。',
        progress: 100,
        status: 'completed' as ProjectDetailData['status'],
        members: [
            { id: 'user3', name: '王五', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=WangWu' },
            { id: 'user4', name: '赵六', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ZhaoLiu' },
        ],
        createdAt: '2024-03-15T08:00:00Z',
        updatedAt: '2024-04-05T16:45:00Z',
        tags: ['古风', '传统音乐', '诗词', '已完成'],
        creator: { id: 'user3', name: '王五', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=WangWu' },
        neededRoles: [
            { role: 'composer' as UserRole, filled: true },
            { role: 'lyricist' as UserRole, filled: true },
            { role: 'singer' as UserRole, filled: true },
            { role: 'arranger' as UserRole, filled: true },
            { role: 'mixer' as UserRole, filled: true },
        ],
        uploadedFiles: [
            { id: 'f_proj2_1', name: '乐谱终稿.pdf', type: 'score' as UploadedFile['type'], uploader: { id: 'user3', name: '王五', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=WangWu' }, uploadedAt: '2024-04-04T10:00:00Z', fileUrl: '#', folderId: 'folder2-2' },
            { id: 'f_proj2_2', name: '成品混音.wav', type: 'audio' as UploadedFile['type'], uploader: { id: 'user4', name: '赵六', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ZhaoLiu' }, uploadedAt: '2024-04-05T16:40:00Z', fileUrl: '#', folderId: 'folder2-1' },
            { id: 'f_proj2_3', name: '古筝分轨.mp3', type: 'audio' as UploadedFile['type'], uploader: { id: 'user3', name: '王五', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=WangWu' }, uploadedAt: '2024-03-28T11:00:00Z', fileUrl: '#', folderId: 'folder2-3' },
        ],
        discussion: [
            { id: 'd_proj2_1', author: { id: 'user4', name: '赵六', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ZhaoLiu' }, content: '最终混音版本上传了，这个项目圆满结束！感谢合作！', timestamp: '2024-04-05T16:42:00Z' },
        ],
        timeline: mockTimelineProj2,
    },
    {
        id: 'proj3',
        name: '电影配乐片段征集',
        description: '为一部独立短片创作1分钟的情感配乐片段，风格不限。',
        progress: 10,
        status: 'ongoing' as ProjectDetailData['status'],
        members: [
            { id: 'user5', name: '导演小明', avatar: 'https://api.dicebear.com/7.x/bottts/svg?seed=DirectorMing' },
        ],
        createdAt: '2024-04-12T09:00:00Z',
        updatedAt: '2024-04-12T09:30:00Z',
        tags: ['电影配乐', '短片', '情感', '风格不限', '招募作曲', '招募编曲'],
        creator: { id: 'user5', name: '导演小明', avatar: 'https://api.dicebear.com/7.x/bottts/svg?seed=DirectorMing' },
        neededRoles: [
            { role: 'composer' as UserRole, filled: false },
            { role: 'arranger' as UserRole, filled: false },
            { role: 'mixer' as UserRole, filled: false },
        ],
        uploadedFiles: [
            { id: 'f_proj3_1', name: '影片片段参考.mp4', type: 'video' as UploadedFile['type'], uploader: { id: 'user5', name: '导演小明', avatar: 'https://api.dicebear.com/7.x/bottts/svg?seed=DirectorMing' }, uploadedAt: '2024-04-12T09:15:00Z', fileUrl: '#', folderId: 'folder3-1' },
            { id: 'f_proj3_2', name: '氛围音效参考1.ogg', type: 'audio' as UploadedFile['type'], uploader: { id: 'user5', name: '导演小明', avatar: 'https://api.dicebear.com/7.x/bottts/svg?seed=DirectorMing' }, uploadedAt: '2024-04-13T10:00:00Z', fileUrl: '#', folderId: 'folder3-1' },
        ],
        discussion: [
            { id: 'd_proj3_1', author: { id: 'user5', name: '导演小明', avatar: 'https://api.dicebear.com/7.x/bottts/svg?seed=DirectorMing' }, content: '大家好，这是短片的参考片段，需要一段大约1分钟的情感配乐，期待大家的创作！', timestamp: '2024-04-12T09:35:00Z' },
        ],
        timeline: mockTimelineProj3,
    },
];

// ---------- Data Fetching Function ----------

// 模拟 API 获取包含时间线的项目工作区数据
export const fetchProjectWorkspaceData = (projectId: string): Promise<ProjectWorkspaceData | null> => {
    console.log(`Fetching workspace data for project: ${projectId}`);
    return new Promise((resolve) => {
        setTimeout(() => {
            const projectData = mockProjectsWorkspaceData.find(p => p.id === projectId);
            if (projectData) {
                resolve(projectData);
            } else {
                resolve(null); // Not found
            }
        }, 300); // Simulate network delay
    });
};

// Helper to get folders for a specific project (used in FileArea)
export const getMockFoldersForProject = (projectId: string): FileFolder[] => {
    return mockFolders.filter(folder => folder.projectId === projectId);
}; 