import { SystemNotification, ChatConversation, ChatMessage, User } from '../types';
import { mockUserProfile } from '../pages/Profile'; // 获取当前模拟用户

const currentUser = mockUserProfile; // Demo user

// --- 模拟其他用户 ---
const otherUser1: Pick<User, 'id' | 'name' | 'avatar'> = {
    id: 'user1',
    name: '张三',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ZhangSan'
};
const otherUser2: Pick<User, 'id' | 'name' | 'avatar'> = {
    id: 'user3',
    name: '王五',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=WangWu'
};

// --- 模拟系统通知 ---
export const mockSystemNotifications: SystemNotification[] = [
    {
        id: 'sn1',
        type: 'application_status',
        title: '项目申请已通过',
        content: `您申请加入项目 "夏日清凉音乐节" 已被创建者 张三 通过。`,
        timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
        isRead: false,
        link: '/projects/proj1'
    },
    {
        id: 'sn2',
        type: 'new_comment',
        title: '项目 "古风音乐创作" 有新评论',
        content: '赵六 评论了：最终混音版本上传了，这个项目圆满结束！感谢合作！',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(),
        isRead: false,
        link: '/projects/proj2' // Link to project discussion
    },
    {
        id: 'sn3',
        type: 'project_update',
        title: '项目 "电影配乐片段征集" 有新文件上传',
        content: '导演小明 上传了文件 "影片片段参考.mp4"',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 25).toISOString(),
        isRead: true,
        link: '/projects/proj3'
    },
    {
        id: 'sn4',
        type: 'system_announcement',
        title: '平台维护通知',
        content: 'Ibom平台计划于下周二凌晨2点进行系统维护，预计持续1小时。',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(),
        isRead: true,
    },
];

// --- 模拟聊天会话 ---
export const mockConversations: ChatConversation[] = [
    {
        id: 'conv1',
        participants: [currentUser, otherUser1],
        lastMessage: {
            content: '好的，我尽快看一下歌词。',
            timestamp: new Date(Date.now() - 1000 * 60 * 20).toISOString(),
            senderName: currentUser.name // Last message from current user
        },
        unreadCount: 0,
    },
    {
        id: 'conv2',
        participants: [currentUser, otherUser2],
        lastMessage: {
            content: '没问题，很高兴和你合作！项目已完成。',
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(),
            senderName: otherUser2.name // Last message from Wang Wu
        },
        unreadCount: 1, // One unread message
    },
];

// --- 模拟聊天消息 ---
export const mockChatMessages: Record<string, ChatMessage[]> = {
    'conv1': [
        {
            id: 'msg1', conversationId: 'conv1',
            senderId: otherUser1.id, senderName: otherUser1.name, senderAvatar: otherUser1.avatar,
            content: '嘿，你的歌词写得真不错！很有感觉。',
            timestamp: new Date(Date.now() - 1000 * 60 * 25).toISOString()
        },
        {
            id: 'msg2', conversationId: 'conv1',
            senderId: currentUser.id, senderName: currentUser.name, senderAvatar: currentUser.avatar,
            content: '谢谢！我把初稿发给你了，有空看看？',
            timestamp: new Date(Date.now() - 1000 * 60 * 22).toISOString()
        },
        {
            id: 'msg3', conversationId: 'conv1',
            senderId: otherUser1.id, senderName: otherUser1.name, senderAvatar: otherUser1.avatar,
            content: '收到了，今晚看看。',
            timestamp: new Date(Date.now() - 1000 * 60 * 21).toISOString()
        },
        {
            id: 'msg4', conversationId: 'conv1',
            senderId: currentUser.id, senderName: currentUser.name, senderAvatar: currentUser.avatar,
            content: '好的，我尽快看一下歌词。', // Matches lastMessage in conv1
            timestamp: new Date(Date.now() - 1000 * 60 * 20).toISOString()
        },
    ],
    'conv2': [
        {
            id: 'msg5', conversationId: 'conv2',
            senderId: currentUser.id, senderName: currentUser.name, senderAvatar: currentUser.avatar,
            content: '王五老师，古风那个项目我们合作的部分已经完成了，非常感谢！',
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4.5).toISOString()
        },
        {
            id: 'msg6', conversationId: 'conv2',
            senderId: otherUser2.id, senderName: otherUser2.name, senderAvatar: otherUser2.avatar,
            content: '不客气，你的部分也很出色。', // Unread message
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4.1).toISOString()
        },
        {
            id: 'msg7', conversationId: 'conv2',
            senderId: otherUser2.id, senderName: otherUser2.name, senderAvatar: otherUser2.avatar,
            content: '没问题，很高兴和你合作！项目已完成。', // Matches lastMessage in conv2
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString()
        },
    ],
}; 