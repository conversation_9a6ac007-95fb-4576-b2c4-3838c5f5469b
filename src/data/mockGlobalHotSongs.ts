import { GlobalHotSong } from '../types';

// Mock data for Global Hot Songs (Top 12 for demo)
export const mockGlobalHotSongsData: GlobalHotSong[] = [
    {
        id: 'ghs1', rank: 1, title: '星辰大海', artists: ['徐佳莹'],
        coverArt: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745?auto=format&fit=crop&w=100&q=80', heatScore: 99876
    },
    {
        id: 'ghs2', rank: 2, title: '午夜的士高', artists: ['新裤子'],
        coverArt: 'https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?auto=format&fit=crop&w=100&q=80', heatScore: 98543
    },
    {
        id: 'ghs3', rank: 3, title: '飞鸟和蝉', artists: ['任然'],
        coverArt: 'https://images.unsplash.com/photo-1483000805330-4eaf0a0d52da?auto=format&fit=crop&w=100&q=80', heatScore: 97123
    },
    {
        id: 'ghs4', rank: 4, title: '海底', artists: ['一支榴莲'],
        coverArt: 'https://images.unsplash.com/photo-1460723237483-7a6dc9d0b212?auto=format&fit=crop&w=100&q=80', heatScore: 96888
    },
    {
        id: 'ghs5', rank: 5, title: '漠河舞厅', artists: ['柳爽'],
        coverArt: 'https://images.unsplash.com/photo-1586034455504-6087132fba5e?auto=format&fit=crop&w=100&q=80', heatScore: 95010
    },
    {
        id: 'ghs6', rank: 6, title: '错位时空', artists: ['艾辰'],
        coverArt: 'https://images.unsplash.com/photo-1508700115892-45ecd05ae2ad?auto=format&fit=crop&w=100&q=80', heatScore: 94500
    },
    {
        id: 'ghs7', rank: 7, title: '大风吹', artists: ['王赫野'],
        coverArt: 'https://images.unsplash.com/photo-1513106859204-4c6c6a3c4a1a?auto=format&fit=crop&w=100&q=80', heatScore: 93200
    },
    {
        id: 'ghs8', rank: 8, title: '删了吧', artists: ['烟(許佳豪)'],
        coverArt: 'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?auto=format&fit=crop&w=100&q=80', heatScore: 92100
    },
    {
        id: 'ghs9', rank: 9, title: '白月光与朱砂痣', artists: ['大籽'],
        coverArt: 'https://images.unsplash.com/photo-1547891654-e66ed7ebb968?auto=format&fit=crop&w=100&q=80', heatScore: 91500
    },
    {
        id: 'ghs10', rank: 10, title: '清空', artists: ['王忻辰', '苏星婕'],
        coverArt: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?auto=format&fit=crop&w=100&q=80', heatScore: 90800
    },
    // --- Folded initially ---
    {
        id: 'ghs11', rank: 11, title: '嘉宾', artists: ['张远'],
        coverArt: 'https://images.unsplash.com/photo-1484876065684-b683cf17a170?auto=format&fit=crop&w=100&q=80', heatScore: 89500
    },
    {
        id: 'ghs12', rank: 12, title: '沦陷', artists: ['王靖雯不胖'],
        coverArt: 'https://images.unsplash.com/photo-1558022657-96f8d919f8ce?auto=format&fit=crop&w=100&q=80', heatScore: 88600
    },
    {
        id: 'ghs13', rank: 13, title: '歌曲 13', artists: ['艺术家 13'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 87850
    },
    {
        id: 'ghs14', rank: 14, title: '歌曲 14', artists: ['艺术家 14'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 87100
    },
    {
        id: 'ghs15', rank: 15, title: '歌曲 15', artists: ['艺术家 15'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 86350
    },
    {
        id: 'ghs16', rank: 16, title: '歌曲 16', artists: ['艺术家 16'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 85600
    },
    {
        id: 'ghs17', rank: 17, title: '歌曲 17', artists: ['艺术家 17'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 84850
    },
    {
        id: 'ghs18', rank: 18, title: '歌曲 18', artists: ['艺术家 18'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 84100
    },
    {
        id: 'ghs19', rank: 19, title: '歌曲 19', artists: ['艺术家 19'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 83350
    },
    {
        id: 'ghs20', rank: 20, title: '歌曲 20', artists: ['艺术家 20'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 82600
    },
    {
        id: 'ghs21', rank: 21, title: '歌曲 21', artists: ['艺术家 21'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 81850
    },
    {
        id: 'ghs22', rank: 22, title: '歌曲 22', artists: ['艺术家 22'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 81100
    },
    {
        id: 'ghs23', rank: 23, title: '歌曲 23', artists: ['艺术家 23'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 80350
    },
    {
        id: 'ghs24', rank: 24, title: '歌曲 24', artists: ['艺术家 24'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 79600
    },
    {
        id: 'ghs25', rank: 25, title: '歌曲 25', artists: ['艺术家 25'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 78850
    },
    {
        id: 'ghs26', rank: 26, title: '歌曲 26', artists: ['艺术家 26'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 78100
    },
    {
        id: 'ghs27', rank: 27, title: '歌曲 27', artists: ['艺术家 27'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 77350
    },
    {
        id: 'ghs28', rank: 28, title: '歌曲 28', artists: ['艺术家 28'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 76600
    },
    {
        id: 'ghs29', rank: 29, title: '歌曲 29', artists: ['艺术家 29'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 75850
    },
    {
        id: 'ghs30', rank: 30, title: '歌曲 30', artists: ['艺术家 30'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 75100
    },
    {
        id: 'ghs31', rank: 31, title: '歌曲 31', artists: ['艺术家 31'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 74350
    },
    {
        id: 'ghs32', rank: 32, title: '歌曲 32', artists: ['艺术家 32'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 73600
    },
    {
        id: 'ghs33', rank: 33, title: '歌曲 33', artists: ['艺术家 33'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 72850
    },
    {
        id: 'ghs34', rank: 34, title: '歌曲 34', artists: ['艺术家 34'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 72100
    },
    {
        id: 'ghs35', rank: 35, title: '歌曲 35', artists: ['艺术家 35'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 71350
    },
    {
        id: 'ghs36', rank: 36, title: '歌曲 36', artists: ['艺术家 36'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 70600
    },
    {
        id: 'ghs37', rank: 37, title: '歌曲 37', artists: ['艺术家 37'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 69850
    },
    {
        id: 'ghs38', rank: 38, title: '歌曲 38', artists: ['艺术家 38'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 69100
    },
    {
        id: 'ghs39', rank: 39, title: '歌曲 39', artists: ['艺术家 39'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 68350
    },
    {
        id: 'ghs40', rank: 40, title: '歌曲 40', artists: ['艺术家 40'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 67600
    },
    {
        id: 'ghs41', rank: 41, title: '歌曲 41', artists: ['艺术家 41'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 66850
    },
    {
        id: 'ghs42', rank: 42, title: '歌曲 42', artists: ['艺术家 42'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 66100
    },
    {
        id: 'ghs43', rank: 43, title: '歌曲 43', artists: ['艺术家 43'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 65350
    },
    {
        id: 'ghs44', rank: 44, title: '歌曲 44', artists: ['艺术家 44'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 64600
    },
    {
        id: 'ghs45', rank: 45, title: '歌曲 45', artists: ['艺术家 45'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 63850
    },
    {
        id: 'ghs46', rank: 46, title: '歌曲 46', artists: ['艺术家 46'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 63100
    },
    {
        id: 'ghs47', rank: 47, title: '歌曲 47', artists: ['艺术家 47'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 62350
    },
    {
        id: 'ghs48', rank: 48, title: '歌曲 48', artists: ['艺术家 48'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 61600
    },
    {
        id: 'ghs49', rank: 49, title: '歌曲 49', artists: ['艺术家 49'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 60850
    },
    {
        id: 'ghs50', rank: 50, title: '歌曲 50', artists: ['艺术家 50'],
        coverArt: 'https://images.unsplash.com/photo-1494232410401-ad00d5433cfa?auto=format&fit=crop&w=100&q=80', heatScore: 60100
    }
];

export default mockGlobalHotSongsData; 