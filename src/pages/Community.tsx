import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { PlusCircle } from 'lucide-react';
import { Tabs, TabPanel } from '../components/ui/Tabs';
import { PostList } from '../components/Community/PostList';
import { UserInteraction } from '../components/Community/UserInteraction';
import { TrendingTags } from '../components/Community/TrendingTags';
import { CategoryList } from '../components/Community/CategoryList';
import { HotActivities } from '../components/Community/HotActivities';
import { LiveStreaming } from '../components/Community/LiveStreaming';
import { AuthState, PostCategory, POST_CATEGORIES } from '../types';

interface CommunityProps {
    auth: AuthState;
}

export const Community: React.FC<CommunityProps> = ({ auth }) => {
    const [selectedCategory, setSelectedCategory] = useState<PostCategory | 'all'>('all');
    const [activeTab, setActiveTab] = useState<number>(0);
    const navigate = useNavigate();

    const handleCreatePostClick = () => {
        navigate('/community/create');
    };

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="max-w-4xl mx-auto">
                <div className="flex items-center justify-between mb-8">
                    <h1 className="text-3xl font-bold text-gray-900">音乐人社区</h1>
                    {auth.isAuthenticated && (
                        <button
                            onClick={handleCreatePostClick}
                            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium shadow-sm"
                        >
                            <PlusCircle className="w-4 h-4 mr-1.5" />
                            发布新帖
                        </button>
                    )}
                </div>

                <HotActivities />

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <div className="lg:col-span-2">
                        <LiveStreaming />

                        <div className="mb-6">
                            <Tabs
                                className="flex-shrink-0"
                                value={activeTab}
                                onChange={setActiveTab}
                            >
                                <TabPanel label="最新动态">
                                    <PostList auth={auth} sortBy="latest" selectedCategory={selectedCategory} />
                                </TabPanel>
                                <TabPanel label="热门话题">
                                    <PostList auth={auth} sortBy="hot" selectedCategory={selectedCategory} />
                                </TabPanel>
                                <TabPanel label="关注动态">
                                    <PostList auth={auth} sortBy="following" selectedCategory={selectedCategory} />
                                </TabPanel>
                            </Tabs>
                        </div>
                    </div>

                    <div className="space-y-6">
                        <CategoryList
                            categories={POST_CATEGORIES}
                            selectedCategory={selectedCategory}
                            onSelectCategory={setSelectedCategory}
                        />
                        <TrendingTags />
                        {auth.isAuthenticated && (
                            <UserInteraction auth={auth} />
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}; 