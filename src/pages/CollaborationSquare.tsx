import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus } from 'lucide-react';
import { ProjectCard } from '../components/ProjectCard';
import { ProjectFilters } from '../components/CollaborationSquare/ProjectFilters';
import { CreateProjectModal } from '../components/CollaborationSquare/CreateProjectModal';
import { Project, ProjectFormData, UserRole, MusicStyle } from '../types';
import { mockProjectsWorkspaceData } from '../data/mockProjects'; // 导入更新后的共享数据

// 不再需要内部的 mockProjects 定义
// const mockProjects: Project[] = [...];

export const CollaborationSquare: React.FC = () => {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);
  const [selectedStyle, setSelectedStyle] = useState<MusicStyle | null>(null);

  // 过滤项目 - 使用共享数据，并实现基于 Tag/Role 的过滤（如果 ProjectFilters 支持）
  const filteredProjects = mockProjectsWorkspaceData.filter(project => {
    // 示例：基于角色的过滤 (需要 ProjectFilters 提供 selectedRole)
    // 注意：Project 类型现在没有直接的 needed 角色列表，可能需要检查 tags
    const neededRolesTags = project.tags.filter(tag => tag.startsWith('招募')).map(tag => tag.replace('招募', ''));
    if (selectedRole && !neededRolesTags.some(neededRoleTag => roleLabels[selectedRole as UserRole]?.includes(neededRoleTag))) {
      // 简单的基于标签的检查，可能不完全准确
      // return false; // 如果需要激活过滤，取消此行注释
    }

    // 示例：基于风格的过滤 (需要 ProjectFilters 提供 selectedStyle)
    // 注意：Project 类型现在没有 style 字段，需要检查 tags
    if (selectedStyle && !project.tags.some(tag => tag.toLowerCase() === selectedStyle.toLowerCase())) {
      // return false; // 如果需要激活过滤，取消此行注释
    }

    // 默认不过滤，显示所有项目
    return true;
  });

  // 辅助函数，仅用于过滤示例
  const roleLabels: Record<UserRole, string> = {
    lyricist: '作词',
    composer: '作曲',
    singer: '演唱',
    arranger: '编曲',
    mixer: '混音',
    producer: '制作',
  };

  const handleCreateProject = (data: ProjectFormData) => {
    console.log('New project data:', data);
  };

  const handleViewDetails = (projectId: string) => {
    navigate(`/projects/${projectId}`);
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900">协作广场</h1>
        <button
          onClick={() => setIsModalOpen(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-5 h-5 mr-2" />
          发起协作
        </button>
      </div>

      <ProjectFilters
        selectedRole={selectedRole}
        selectedStyle={selectedStyle}
        onRoleChange={setSelectedRole}
        onStyleChange={setSelectedStyle}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
        {filteredProjects.map(project => (
          <ProjectCard
            key={project.id}
            project={project}
            onClick={() => handleViewDetails(project.id)}
          />
        ))}
      </div>

      <CreateProjectModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleCreateProject}
      />
    </div>
  );
};