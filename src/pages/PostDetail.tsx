import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { CommunityPost, DiscussionComment, AuthState, UserSummary } from '../types';
import { ArrowLeft, Send, ThumbsUp, Share2, Bookmark, Tag, UserPlus, Check } from 'lucide-react';
import { CommentItem, CommentWithReplies } from '../components/Community/CommentItem';
// import { DiscussionArea } from '../components/DiscussionArea'; // Placeholder

// --- Mock Data --- (Replace with API call later)
const mockPostData: CommunityPost = {
    id: 'p1',
    title: '求助：如何让我的混音更有空间感？',
    author: {
        id: 'u1',
        name: '混音小白',
        avatar: 'https://api.dicebear.com/7.x/fun-emoji/svg?seed=Felix'
    },
    timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
    snippet: '我尝试了各种混响和延迟，但总感觉声音挤在一起，不够开阔... 这是帖子的完整内容，可以更长一些，包含代码示例或者详细的步骤说明等等。',
    commentCount: 2,
    likeCount: 15,
    link: '/community/post/p1',
    tags: ['#混音', '#空间感', '#求助', '#制作技巧'],
    // Assuming full content might be different or richer than snippet
    content: `
## 我的问题

大家好，我最近在混音一个项目，遇到了一个棘手的问题：无论我怎么调整，总感觉声音元素（鼓、贝斯、合成器、人声）都挤在一起，缺乏清晰度和空间感，听起来很"平"。

## 我尝试过的方法

1.  **EQ调整**：我已经尝试为每个音轨 carve out 空间，衰减冲突频率，提升一些高频来增加空气感。
2.  **声像 (Panning)**：我也尝试将不同的乐器放置在立体声声场的不同位置。
3.  **混响 (Reverb)**：使用了不同类型的混响（房间、大厅、板式）并尝试了不同的预延迟和衰减时间。
4.  **延迟 (Delay)**：尝试了短延迟来增加厚度，长延迟来创造空间感。

## 困惑点

尽管做了这些尝试，整体听起来还是不尽人意。是不是我哪里做得不对？或者有什么更高级的技巧可以用来创造深度和宽度？比如 Mid/Side EQ？或者特定的混响技巧？

非常感谢任何建议！
    `,
};

const mockCommentsData: DiscussionComment[] = [
    {
        id: 'c1',
        author: {
            id: 'u2',
            name: '节奏玩家',
            avatar: 'https://api.dicebear.com/7.x/shapes/svg?seed=Jasper'
        },
        content: '试试看用 Mid/Side EQ 处理一下低频？把 Side 的低频切掉一些，可以让中心更清晰。',
        timestamp: new Date(Date.now() - 1000 * 60 * 10).toISOString(),
        // No parentId - top level comment
    },
    {
        id: 'c2',
        author: {
            id: 'u3',
            name: '科技探索者',
            avatar: 'https://api.dicebear.com/7.x/identicon/svg?seed=Bandit'
        },
        content: '同意楼上。另外，检查一下你的混响发送量，有时候混响加太多也会让声音变糊。可以尝试用多个不同类型的短混响来代替一个长混响。',
        timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
        // No parentId - top level comment
    },
    {
        id: 'c3',
        author: {
            id: 'u1', // Original poster replies
            name: '混音小白',
            avatar: 'https://api.dicebear.com/7.x/fun-emoji/svg?seed=Felix'
        },
        content: '感谢建议！Mid/Side EQ 这个我试试看。混响确实有可能用多了，我再检查下。',
        timestamp: new Date(Date.now() - 1000 * 60 * 3).toISOString(),
        parentId: 'c1' // Reply to comment c1
    },
    {
        id: 'c4',
        author: {
            id: 'u2',
            name: '节奏玩家',
            avatar: 'https://api.dicebear.com/7.x/shapes/svg?seed=Jasper'
        },
        content: '不客气！希望有帮助。',
        timestamp: new Date(Date.now() - 1000 * 60 * 2).toISOString(),
        parentId: 'c3' // Reply to comment c3 (which is a reply to c1)
    }
];
// --- End Mock Data ---

interface PostDetailProps {
    auth: AuthState;
}

export const PostDetail: React.FC<PostDetailProps> = ({ auth }) => {
    const { postId } = useParams<{ postId: string }>();
    const [post, setPost] = useState<CommunityPost | null>(null);
    const [comments, setComments] = useState<DiscussionComment[]>([]);
    const [loading, setLoading] = useState(true);
    const [newComment, setNewComment] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isLiked, setIsLiked] = useState(false);
    const [isCollected, setIsCollected] = useState(false);
    const [replyingToCommentId, setReplyingToCommentId] = useState<string | null>(null);
    const [localFollowingState, setLocalFollowingState] = useState<Set<string>>(new Set());

    useEffect(() => {
        setLoading(true);
        // Simulate API call
        setTimeout(() => {
            if (postId === mockPostData.id) { // Find mock post by ID
                setPost(mockPostData);
                setComments(mockCommentsData);
                // Simulate fetching user's like/collect status for this post
                // In a real app, this would come from user data or API
                setIsLiked(Math.random() > 0.5); // Random initial state
                setIsCollected(Math.random() > 0.5); // Random initial state
            } else {
                setPost(null); // Handle post not found
                setComments([]);
            }
            setLoading(false);
        }, 500); // Simulate network delay
    }, [postId]);

    useEffect(() => {
        if (auth.isAuthenticated && auth.user) {
            setLocalFollowingState(new Set(auth.user.following.map(u => u.id)));
        }
    }, [auth.user, auth.isAuthenticated]);

    const handlePostCommentOrReply = (content: string, parentId?: string) => {
        if (!auth.isAuthenticated || !auth.user || !content.trim() || isSubmitting) return;

        setIsSubmitting(true);
        const commentToAdd: DiscussionComment = {
            id: `c${Date.now()}`,
            author: { id: auth.user.id, name: auth.user.name, avatar: auth.user.avatar },
            content: content.trim(),
            timestamp: new Date().toISOString(),
            parentId: parentId,
        };

        setTimeout(() => {
            setComments(prev => [commentToAdd, ...prev]);
            setNewComment('');
            setReplyingToCommentId(null);
            setIsSubmitting(false);
            console.log("New comment/reply posted (simulated):", commentToAdd);
        }, 300);
    };

    const handleTopLevelSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        handlePostCommentOrReply(newComment);
    };

    const handleLike = () => {
        if (!auth.isAuthenticated) return;
        setIsLiked(!isLiked);
        setPost(prevPost => prevPost ? { ...prevPost, likeCount: isLiked ? prevPost.likeCount - 1 : prevPost.likeCount + 1 } : null);
        console.log(`Toggled like for post ${postId}. New status: ${!isLiked}`);
    };

    const handleCollect = () => {
        if (!auth.isAuthenticated) return;
        setIsCollected(!isCollected);
        console.log(`Toggled collect for post ${postId}. New status: ${!isCollected}`);
    };

    const handleShare = () => {
        navigator.clipboard.writeText(window.location.href);
        alert('帖子链接已复制到剪贴板！');
        console.log(`Sharing post ${postId}`);
    };

    const handleFollowToggle = (targetUserId: string) => {
        if (!auth.isAuthenticated) return;

        const isCurrentlyFollowing = localFollowingState.has(targetUserId);
        console.log(`[PostDetail] Attempting to ${isCurrentlyFollowing ? 'unfollow' : 'follow'} user: ${targetUserId}`);

        setLocalFollowingState(prev => {
            const newSet = new Set(prev);
            if (isCurrentlyFollowing) {
                newSet.delete(targetUserId);
            } else {
                newSet.add(targetUserId);
            }
            return newSet;
        });
        // In a real app, call API here
    };

    const buildCommentTree = (commentList: DiscussionComment[]): CommentWithReplies[] => {
        const commentMap: { [id: string]: CommentWithReplies } = {};
        const tree: CommentWithReplies[] = [];

        commentList.forEach(comment => {
            commentMap[comment.id] = { ...comment, replies: [] };
        });

        commentList.forEach(comment => {
            if (comment.parentId && commentMap[comment.parentId]) {
                commentMap[comment.parentId].replies?.push(commentMap[comment.id]);
            } else {
                tree.push(commentMap[comment.id]);
            }
        });

        tree.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        Object.values(commentMap).forEach(comment => {
            comment.replies?.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        });

        return tree;
    };

    const commentTree = buildCommentTree(comments);

    if (loading) {
        return <div className="container mx-auto px-4 py-8 text-center">加载中...</div>;
    }

    if (!post) {
        return <div className="container mx-auto px-4 py-8 text-center">帖子未找到或已被删除。</div>;
    }

    const isPostAuthorSelf = auth.user?.id === post.author?.id;
    const isFollowingPostAuthor = localFollowingState.has(post.author?.id ?? '');

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="max-w-3xl mx-auto">
                <Link to="/community" className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-4">
                    <ArrowLeft className="w-4 h-4 mr-1" />
                    返回社群
                </Link>

                <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
                    <h1 className="text-2xl font-bold text-gray-900 mb-3">{post.title}</h1>
                    <div className="flex items-center space-x-2 text-sm text-gray-500 mb-4">
                        <img src={post.author.avatar} alt={post.author.name} className="w-6 h-6 rounded-full" />
                        <span>{post.author.name}</span>
                        {auth.isAuthenticated && !isPostAuthorSelf && (
                            <button
                                onClick={() => handleFollowToggle(post.author.id)}
                                className={`px-1.5 py-0 rounded text-xs transition-colors ${isFollowingPostAuthor
                                    ? 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                                    : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                                    }`}
                            >
                                {isFollowingPostAuthor ? (
                                    <Check className="w-3 h-3 inline-block mr-0.5" />
                                ) : (
                                    <UserPlus className="w-3 h-3 inline-block mr-0.5" />
                                )}
                                {isFollowingPostAuthor ? '已关注' : '关注'}
                            </button>
                        )}
                        <span>•</span>
                        <span>{new Date(post.timestamp).toLocaleString('zh-CN')}</span>
                    </div>
                    <div className="prose max-w-none text-gray-700 whitespace-pre-wrap mb-4">
                        {post.content || post.snippet}
                    </div>

                    {post.tags && post.tags.length > 0 && (
                        <div className="flex flex-wrap gap-2 mb-5 pt-4 border-t border-gray-100">
                            {post.tags.map((tag, index) => (
                                <Link
                                    key={index}
                                    to={`/community/tags/${encodeURIComponent(tag.substring(1))}`}
                                    className="px-2.5 py-1 bg-blue-50 text-blue-700 rounded-full text-xs font-medium hover:bg-blue-100 transition-colors"
                                >
                                    {tag}
                                </Link>
                            ))}
                        </div>
                    )}

                    <div className={`flex items-center space-x-5 ${post.tags && post.tags.length > 0 ? 'pt-4 border-t border-gray-100' : ''}`}>
                        <button
                            onClick={handleLike}
                            className={`flex items-center space-x-1.5 transition-colors ${isLiked ? 'text-blue-600' : 'text-gray-500 hover:text-blue-600'
                                }`}
                        >
                            <ThumbsUp className={`w-5 h-5 ${isLiked ? 'fill-current' : ''}`} />
                            <span className="text-sm font-medium">{post.likeCount}</span>
                        </button>
                        <button
                            onClick={handleCollect}
                            className={`flex items-center space-x-1.5 transition-colors ${isCollected ? 'text-yellow-600' : 'text-gray-500 hover:text-yellow-600'
                                }`}
                        >
                            <Bookmark className={`w-5 h-5 ${isCollected ? 'fill-current' : ''}`} />
                            <span className="text-sm font-medium">收藏</span>
                        </button>
                        <button
                            onClick={handleShare}
                            className="flex items-center space-x-1.5 text-gray-500 hover:text-blue-600 transition-colors"
                        >
                            <Share2 className="w-5 h-5" />
                            <span className="text-sm font-medium">分享</span>
                        </button>
                    </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm p-6">
                    <h2 className="text-xl font-semibold mb-4">评论 ({comments.length})</h2>

                    {auth.isAuthenticated && auth.user ? (
                        <form onSubmit={handleTopLevelSubmit} className="flex items-start space-x-3 mb-6">
                            <img
                                src={auth.user.avatar}
                                alt={auth.user.name}
                                className="w-9 h-9 rounded-full bg-gray-100 mt-1 flex-shrink-0"
                            />
                            <div className="flex-1 relative">
                                <textarea
                                    value={newComment}
                                    onChange={(e) => setNewComment(e.target.value)}
                                    placeholder="发表你的评论..."
                                    rows={3}
                                    className="w-full border border-gray-200 rounded-lg p-3 pr-16 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                                />
                                <button
                                    type="submit"
                                    disabled={!newComment.trim() || isSubmitting}
                                    className="absolute right-3 bottom-3 px-3 py-1.5 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center"
                                >
                                    <Send className="w-4 h-4 mr-1.5" />
                                    {isSubmitting ? '发布中...' : '发布'}
                                </button>
                            </div>
                        </form>
                    ) : (
                        <p className="text-center text-gray-500 mb-6">请 <Link to="/login" className="text-blue-600 hover:underline">登录</Link> 后发表评论。</p>
                    )}

                    <div className="space-y-0">
                        {commentTree.length > 0 ? (
                            commentTree.map(comment => (
                                <CommentItem
                                    key={comment.id}
                                    comment={comment}
                                    auth={auth}
                                    allComments={comments}
                                    onReplySubmit={handlePostCommentOrReply}
                                    replyingToId={replyingToCommentId}
                                    setReplyingToId={setReplyingToCommentId}
                                />
                            ))
                        ) : (
                            <p className="text-center text-gray-500 py-6">暂无评论。</p>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}; 