import React, { useState, useEffect } from 'react';
import { <PERSON>R<PERSON>, <PERSON>rk<PERSON>, Clock, Users, TrendingUp } from 'lucide-react';
import { Project, UserRole, HotSong, AuthState } from '../types';
import { fetchHotSongs } from '../data/mockHotSongs';
import { Banner } from '../components/Banner';
import { FeaturedProjects } from '../components/FeaturedProjects';
import { GlobalTrends } from '../components/GlobalTrends';
import { Footer } from '../components/Footer';
import { ProjectCard } from '../components/ProjectCard';
import { HotSongs } from '../components/HotSongs';
import { CommunityInteraction } from '../components/CommunityInteraction';
import { GlobalHotSongsList } from '../components/GlobalHotSongsList';

// Mock data for demonstration
const recommendedProjectsData: Project[] = [
  {
    id: 'rec1',
    name: '寻找灵魂歌手演绎《午夜蓝调》',
    description: '一首充满情感的蓝调作品，需要有独特嗓音的歌手来诠释',
    progress: 60,
    status: 'ongoing',
    members: [
      { id: 'm7', name: '格蕾丝', avatar: '/avatars/14.jpg' },
      { id: 'm8', name: '亨利', avatar: '/avatars/15.jpg' },
    ],
    createdAt: '2024-04-02T10:00:00Z',
    updatedAt: '2024-04-08T18:00:00Z',
    tags: ['蓝调', '爵士', '人声'],
  },
  {
    id: 'rec2',
    name: '电子音乐制作寻找合作伙伴',
    description: '正在制作一张电子音乐专辑，需要有创意的作曲和编曲',
    progress: 30,
    status: 'ongoing',
    members: [{ id: 'm9', name: '伊莎贝拉', avatar: '/avatars/16.jpg' }],
    createdAt: '2024-03-25T12:00:00Z',
    updatedAt: '2024-04-09T10:00:00Z',
    tags: ['电子', '专辑制作', '编曲'],
  },
];

const recentActivities = [
  {
    id: '1',
    type: 'join',
    projectTitle: '午夜蓝调',
    user: '张三',
    role: 'singer',
    time: '10分钟前',
  },
  {
    id: '2',
    type: 'update',
    projectTitle: '电子音乐专辑',
    user: '李四',
    description: '上传了新的Demo',
    time: '30分钟前',
  },
  {
    id: '3',
    type: 'complete',
    projectTitle: '夏日恋歌',
    user: '王五',
    description: '完成了作词部分',
    time: '2小时前',
  },
];

const trendingTags = [
  { id: '1', name: '流行', count: 128 },
  { id: '2', name: '说唱', count: 89 },
  { id: '3', name: '民谣', count: 67 },
  { id: '4', name: '电子', count: 45 },
];

const platformStats = {
  activeUsers: 1234,
  ongoingProjects: 567,
  completedProjects: 890,
};

interface HomeProps {
  auth: AuthState;
}

export const Home: React.FC<HomeProps> = ({ auth }) => {
  const [hotSongs, setHotSongs] = useState<HotSong[]>([]);
  const [loadingHotSongs, setLoadingHotSongs] = useState(true);
  const [currentPoints, setCurrentPoints] = useState(auth.user?.points || 0);

  useEffect(() => {
    setCurrentPoints(auth.user?.points || 0);
  }, [auth.user?.points]);

  useEffect(() => {
    setLoadingHotSongs(true);
    fetchHotSongs()
      .then(data => {
        setHotSongs(data);
      })
      .catch(err => {
        console.error("Failed to fetch hot songs:", err);
        // Handle error state if needed
      })
      .finally(() => setLoadingHotSongs(false));
  }, []);

  const handlePurchaseSong = (songId: string, cost: number): boolean => {
    if (!auth.isAuthenticated || !auth.user) {
      alert("请先登录！");
      return false;
    }
    if (currentPoints < cost) {
      alert(`积分不足！需要 ${cost} 积分，您当前有 ${currentPoints} 积分。`);
      return false;
    }
    const newPointBalance = currentPoints - cost;
    setCurrentPoints(newPointBalance);
    console.log(`Song ${songId} purchased for ${cost} points. New balance: ${newPointBalance}`);
    return true;
  };

  return (
    <div className="flex flex-col min-h-screen">
      <div className="flex-grow">
        <Banner />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FeaturedProjects />
          {loadingHotSongs ? (
            <div className="text-center py-10">加载热门歌曲中...</div>
          ) : (
            <HotSongs
              hotSongs={hotSongs}
              currentUserPoints={currentPoints}
              onPurchaseSong={handlePurchaseSong}
            />
          )}
          <GlobalTrends />
          <GlobalHotSongsList />
          <CommunityInteraction />

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            {/* Left Column - Recommended Projects */}
            <div className="lg:col-span-2 space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                  <Sparkles className="w-6 h-6 mr-2 text-yellow-500" />
                  为你推荐
                </h2>
                <button className="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                  查看全部
                  <ArrowRight className="w-4 h-4 ml-1" />
                </button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {recommendedProjectsData.map((project) => (
                  <ProjectCard key={project.id} project={project} />
                ))}
              </div>
            </div>

            {/* Right Column - Activity & Trends */}
            <div className="space-y-8">
              {/* Recent Activity */}
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <Clock className="w-5 h-5 mr-2 text-blue-500" />
                  最近动态
                </h3>
                <div className="space-y-4">
                  {recentActivities.map(activity => (
                    <div key={activity.id} className="flex items-start space-x-3 text-sm">
                      <div className="w-2 h-2 rounded-full bg-blue-500 mt-1.5 flex-shrink-0"></div>
                      <div>
                        <p className="text-gray-600 leading-snug">
                          <span className="font-medium text-gray-900">{activity.user}</span>
                          {activity.type === 'join' && ` 以${activity.role}身份加入了 `}
                          {activity.type === 'update' && ` 在 `}
                          {activity.type === 'complete' && ` 在 `}
                          <span className="font-medium text-gray-900">{activity.projectTitle}</span>
                          {activity.description && ` ${activity.description}`}
                        </p>
                        <p className="text-gray-400 text-xs mt-1">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Trending Tags */}
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2 text-rose-500" />
                  热门标签
                </h3>
                <div className="flex flex-wrap gap-2">
                  {trendingTags.map(tag => (
                    <button
                      key={tag.id}
                      className="px-3 py-1 rounded-full bg-gray-100 text-gray-700 text-sm hover:bg-gray-200 transition-colors"
                    >
                      {tag.name}
                      <span className="ml-1 text-gray-500">({tag.count})</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Quick Stats */}
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <Users className="w-5 h-5 mr-2 text-indigo-500" />
                  平台数据
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-indigo-50 rounded-lg">
                    <p className="text-2xl font-bold text-indigo-600">{platformStats.activeUsers.toLocaleString()}</p>
                    <p className="text-sm text-gray-600">活跃用户</p>
                  </div>
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <p className="text-2xl font-bold text-blue-600">{platformStats.ongoingProjects.toLocaleString()}</p>
                    <p className="text-sm text-gray-600">进行中项目</p>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg col-span-2">
                    <p className="text-2xl font-bold text-green-600">{platformStats.completedProjects.toLocaleString()}</p>
                    <p className="text-sm text-gray-600">已完成协作</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};