import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { ProjectWorkspaceData, AuthState } from '../types';
import { fetchProjectWorkspaceData } from '../data/mockProjects'; // Import the new fetch function
import { Loader, Phone, AlertTriangle } from 'lucide-react';

// Import placeholder/actual components (adjust paths as needed)
import { MemberListSidebar } from '../components/ProjectWorkspace/MemberListSidebar';
import { TimelineView } from '../components/ProjectWorkspace/TimelineView';
import { FileArea } from '../components/ProjectWorkspace/FileArea';
import { SimpleDAW } from '../components/ProjectWorkspace/SimpleDAW';
import { VoiceCallModal } from '../components/ProjectWorkspace/VoiceCallModal';
import { Tabs, TabPanel } from '../components/ui/Tabs'; // Import from the new location

interface ProjectWorkspaceProps {
    auth: AuthState;
}

export const ProjectWorkspace: React.FC<ProjectWorkspaceProps> = ({ auth }) => {
    const { projectId } = useParams<{ projectId: string }>();
    const [projectData, setProjectData] = useState<ProjectWorkspaceData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isVoiceModalOpen, setIsVoiceModalOpen] = useState(false);
    const [activeTabIndex, setActiveTabIndex] = useState<number>(0); // State for active tab

    useEffect(() => {
        if (projectId) {
            setLoading(true);
            setError(null);
            fetchProjectWorkspaceData(projectId)
                .then(data => {
                    if (data) {
                        setProjectData(data);
                    } else {
                        setError('无法加载协作空间数据或项目不存在。');
                    }
                })
                .catch(err => {
                    console.error("获取协作空间数据失败:", err);
                    setError('加载协作空间信息时出错。');
                })
                .finally(() => setLoading(false));
        }
    }, [projectId]);

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <Loader className="w-10 h-10 animate-spin text-blue-600" />
                <p className="ml-3 text-gray-600">正在加载协作空间...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center py-10 text-red-600">
                {error}
            </div>
        );
    }

    if (!projectData) {
        return <div className="text-center py-10 text-gray-500">无法显示协作空间。</div>;
    }

    // Extract audio files for DAW
    const audioFiles = projectData.uploadedFiles.filter(f => f.type === 'audio');

    return (
        <div className="flex flex-col md:flex-row gap-6">
            {/* Sidebar */}
            <aside className="w-full md:w-64 flex-shrink-0">
                <MemberListSidebar members={projectData.members} />
            </aside>

            {/* Main Content Area */}
            <div className="flex-grow bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                {/* Header */}
                <div className="flex justify-between items-center mb-4 pb-4 border-b border-gray-200">
                    <h2 className="text-xl font-bold text-gray-800">{projectData.name} - 协作空间</h2>
                    <button
                        onClick={() => setIsVoiceModalOpen(true)}
                        className="inline-flex items-center px-3 py-1.5 bg-green-100 text-green-700 text-sm rounded-lg hover:bg-green-200 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-green-500"
                    >
                        <Phone className="w-4 h-4 mr-1.5" />
                        开始语音通话 (模拟)
                    </button>
                </div>

                {/* Tabs for different sections - Add value and onChange */}
                <Tabs value={activeTabIndex} onChange={setActiveTabIndex}>
                    <TabPanel label="时间线">
                        <TimelineView timeline={projectData.timeline} />
                    </TabPanel>
                    <TabPanel label="文件区">
                        <FileArea files={projectData.uploadedFiles} />
                    </TabPanel>
                    <TabPanel label="简易 DAW">
                        <SimpleDAW audioFiles={audioFiles} />
                    </TabPanel>
                    {/* Add more tabs as needed */}
                </Tabs>
            </div>

            {/* Voice Call Modal */}
            <VoiceCallModal
                isOpen={isVoiceModalOpen}
                onClose={() => setIsVoiceModalOpen(false)}
                members={projectData.members}
            />
        </div>
    );
}; 