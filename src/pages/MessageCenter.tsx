import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { AuthState, UserSummary, ChatConversation, ChatMessage, SystemNotification } from '../types';
import { Search, Send, ArrowLeft, Bell, MessageSquare } from 'lucide-react';
import { SystemNotificationList } from '../components/SystemNotificationList';
import { ConversationList } from '../components/ConversationList';
import { ChatWindow } from '../components/ChatWindow';
import { mockSystemNotifications, mockChatMessages } from '../data/mockMessages';

// --- Mock Data ---
const mockConversations: ChatConversation[] = [
    {
        id: 'conv1',
        participants: [
            { id: 'currentUser123', name: '模拟演示用户', avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=DU' },
            { id: 'u2', name: '节奏玩家', avatar: 'https://api.dicebear.com/7.x/shapes/svg?seed=Jasper' }
        ],
        lastMessage: { content: '没问题，随时联系！', timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(), senderName: '节奏玩家' },
        unreadCount: 0
    },
    {
        id: 'conv2',
        participants: [
            { id: 'currentUser123', name: '模拟演示用户', avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=DU' },
            { id: 'u3', name: '科技探索者', avatar: 'https://api.dicebear.com/7.x/identicon/svg?seed=Bandit' }
        ],
        lastMessage: { content: '关于AI的讨论很有意思，下次继续！', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(), senderName: '科技探索者' },
        unreadCount: 2
    },
];

// Mock user data (needed to find user info based on userId param)
const mockUsers: { [id: string]: UserSummary } = {
    'u1': { id: 'u1', name: '音乐制作人', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=MusicProducer' },
    'u2': { id: 'u2', name: '节奏玩家', avatar: 'https://api.dicebear.com/7.x/shapes/svg?seed=Jasper' },
    'u3': { id: 'u3', name: '科技探索者', avatar: 'https://api.dicebear.com/7.x/identicon/svg?seed=Bandit' }
};
// --- End Mock Data ---

// Re-introduce view type
type MessageView = 'notifications' | 'conversations';

interface MessageCenterProps {
    auth: AuthState;
}

export const MessageCenter: React.FC<MessageCenterProps> = ({ auth }) => {
    const { userId: newChatUserId } = useParams<{ userId: string }>();
    const navigate = useNavigate();

    // Re-introduce view state, default to notifications
    const [view, setView] = useState<MessageView>('notifications');
    const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
    const [recipientInfo, setRecipientInfo] = useState<UserSummary | null>(null);

    // --- Re-introduce state for mock data ---
    const [notifications, setNotifications] = useState<SystemNotification[]>(mockSystemNotifications);
    // Use the already defined mockConversations
    const [conversations, setConversations] = useState<ChatConversation[]>(mockConversations);
    const [messages, setMessages] = useState<Record<string, ChatMessage[]>>(mockChatMessages);
    // ----------------------------------------

    // Effect to handle starting a new chat via URL parameter
    useEffect(() => {
        if (newChatUserId && mockUsers[newChatUserId]) {
            setRecipientInfo(mockUsers[newChatUserId]);
            setSelectedConversationId(null); // Deselect any active conversation
            setView('conversations'); // Switch to conversation view
            console.log("[MessageCenter] Starting new chat, forcing conversations view for user:", newChatUserId);
            // Optional: Navigate to base /messages to clear the URL param visually,
            // but keep the state showing the new chat window.
            // navigate('/messages', { replace: true }); 
        } else {
            // If not starting a new chat, clear recipient info
            setRecipientInfo(null);
        }
    }, [newChatUserId]); // Depend only on the param change

    // Restore original handler functionality for selecting a conversation
    const handleConversationSelect = (convId: string) => {
        setSelectedConversationId(convId);
        setRecipientInfo(null); // Clear any new chat recipient
        setView('conversations'); // Ensure view is conversations
        // Mark conversation as read (simulated)
        setConversations(prev => prev.map(c => c.id === convId ? { ...c, unreadCount: 0 } : c));
        navigate('/messages'); // Navigate to base /messages to clear URL param if any
        console.log("[MessageCenter] Selected conversation:", convId);
    };

    // Restore handlers for notifications and sending messages
    const handleMarkAsRead = (id: string) => {
        setNotifications(prev => prev.map(n => n.id === id ? { ...n, isRead: true } : n));
        console.log("[MessageCenter] Marked notification as read:", id);
    };
    const handleMarkAllAsRead = () => {
        setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
        console.log("[MessageCenter] Marked all notifications as read");
    };

    const handleSendMessage = (conversationId: string, content: string) => {
        // Add comment about temporary IDs
        if (conversationId.startsWith('temp_')) {
            console.warn("[MessageCenter] Sending message for a temporary conversation. Need to implement actual conversation creation on backend.");
            // In a real app, you would likely call an API to create the conversation first,
            // get the real conversationId, and then send the message.
            // For now, we proceed with the temporary ID for mock purposes.
        }

        if (!auth.user || !conversationId) return;
        const newMessage: ChatMessage = {
            id: `msg${Date.now()}`,
            conversationId,
            senderId: auth.user.id,
            senderName: auth.user.name,
            senderAvatar: auth.user.avatar,
            content,
            timestamp: new Date().toISOString(),
        };
        // Update message list for the conversation
        setMessages(prev => ({
            ...prev,
            [conversationId]: [...(prev[conversationId] || []), newMessage]
        }));
        // Update conversation's last message and re-sort
        setConversations(prev => prev.map(c => c.id === conversationId ? {
            ...c,
            lastMessage: { content, timestamp: newMessage.timestamp, senderName: newMessage.senderName }
        } : c).sort((a, b) => new Date(b.lastMessage?.timestamp || 0).getTime() - new Date(a.lastMessage?.timestamp || 0).getTime()));
        console.log("[MessageCenter] Sent message:", newMessage);
    };

    // Find selected conversation data
    const selectedConvObject = conversations.find(c => c.id === selectedConversationId);
    // Get messages for the selected conversation
    const currentChatMessages = selectedConversationId ? messages[selectedConversationId] || [] : [];

    // Determine if chat window should be shown
    const showChatWindow = view === 'conversations' && (recipientInfo !== null || selectedConversationId !== null);

    // Determine the conversation object to pass to ChatWindow
    let chatWindowConversation: ChatConversation | null = selectedConvObject ?? null;
    if (recipientInfo && !selectedConvObject && auth.user) {
        // If starting a new chat, create a temporary conversation object
        chatWindowConversation = {
            id: `temp_${recipientInfo.id}`, // Temporary ID
            participants: [auth.user, recipientInfo],
            lastMessage: null,
            unreadCount: 0
        };
    }

    return (
        <div className="flex h-[calc(100vh-var(--header-height))] border-t">
            {/* Left Column: Sidebar */}
            {/* Hide sidebar on small screens if chat window is shown */}
            <div className={`w-full md:w-1/3 border-r flex flex-col ${showChatWindow ? 'hidden md:flex' : 'flex'}`}>
                {/* View Toggle Buttons - Restored */}
                <div className="flex border-b">
                    <button
                        onClick={() => {
                            setView('notifications');
                            setSelectedConversationId(null);
                            setRecipientInfo(null);
                            navigate('/messages');
                        }}
                        className={`flex-1 flex items-center justify-center p-3 text-sm font-medium transition-colors ${view === 'notifications' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                    >
                        <Bell className="w-4 h-4 mr-1.5" /> 系统通知
                    </button>
                    <button
                        onClick={() => setView('conversations')}
                        className={`flex-1 flex items-center justify-center p-3 text-sm font-medium transition-colors ${view === 'conversations' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                    >
                        <MessageSquare className="w-4 h-4 mr-1.5" /> 私信聊天
                    </button>
                </div>

                {/* Search Bar (Only for conversations view) */}
                {view === 'conversations' && (
                    <div className="p-3 border-b">
                        <div className="relative">
                            <input type="text" placeholder="搜索消息或用户..." className="w-full pl-8 pr-3 py-1.5 border rounded-lg text-sm" />
                            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                        </div>
                    </div>
                )}

                {/* List Area - Conditional Rendering Restored */}
                <div className="flex-1 overflow-y-auto">
                    {view === 'notifications' ? (
                        <SystemNotificationList
                            notifications={notifications}
                            onMarkAsRead={handleMarkAsRead}
                            onMarkAllAsRead={handleMarkAllAsRead}
                        />
                    ) : (
                        <ConversationList
                            conversations={conversations}
                            currentUser={auth.user}
                            selectedConversationId={selectedConversationId}
                            onSelectConversation={handleConversationSelect}
                        />
                    )}
                </div>
            </div>

            {/* Right Column: Main Content Area */}
            {/* Show content area only if sidebar is hidden on mobile, or always on desktop */}
            <div className={`w-full md:w-2/3 flex flex-col bg-gray-50 ${!showChatWindow ? 'hidden md:flex' : 'flex'}`}>
                {view === 'conversations' ? (
                    // --- Conversation View Logic --- 
                    showChatWindow && chatWindowConversation ? (
                        <ChatWindow
                            conversation={chatWindowConversation}
                            messages={currentChatMessages}
                            currentUser={auth.user}
                            onSendMessage={handleSendMessage}
                            onBack={() => { setSelectedConversationId(null); setRecipientInfo(null); setView('conversations'); navigate('/messages'); }}
                        />
                    ) : (
                        <div className="flex-1 flex items-center justify-center text-gray-500">
                            选择一个对话开始聊天。
                        </div>
                    )
                    // --- End Conversation View --- 
                ) : (
                    // --- Notification View Logic --- 
                    <div className="flex-1 flex flex-col items-center justify-center text-gray-500">
                        <Bell size={48} className="mb-4 text-gray-300" />
                        选择一条通知查看详情。
                    </div>
                    // --- End Notification View --- 
                )}
            </div>
        </div>
    );
}; 