import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
    Plus, ListTodo, FolderCheck, Users, MailQuestion, FolderKanban, UsersRound, UserCog,
    UserPlus, PlayCircle, MessageCircle, ThumbsUp, Share2, Star
} from 'lucide-react';

// Import Tabs component if not already (adjust path if needed)
import { Tabs, TabPanel } from '../components/ui/Tabs';
// Import Sidebar
import { QuickActionsSidebar } from '../components/Creator/QuickActionsSidebar'; // Import the created sidebar
import { DataOverviewCard } from '../components/Creator/DataOverviewCard'; // Import the card component
import { CreatorBanner } from '../components/Creator/CreatorBanner'; // Import the banner component

export const CreatorCenterPage: React.FC = () => {
    // Remove the old centered content
    /*
    return (
        <div className="min-h-[70vh] flex flex-col items-center justify-center text-center px-4">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
                创作中心
            </h1>
            <p className="text-lg text-gray-600 mb-10 max-w-md">
                从这里开始，将你的音乐想法变为现实。创建新项目，邀请协作者，一起谱写动人旋律。
            </p>
            <Link
                to="/projects" // Link to the Projects page where users can create a new project
                className="inline-flex items-center px-8 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-lg font-semibold shadow-md transform hover:scale-105 duration-200 ease-in-out"
            >
                <Plus className="w-6 h-6 mr-2" />
                创建新项目
            </Link>
        </div>
    );
    */

    // New Layout
    const [activeTab, setActiveTab] = useState<number>(0);

    // Mock data for overview cards
    const overviewData = {
        ongoingProjects: 5,
        completedProjects: 12,
        totalCollaborators: 28,
        pendingInvites: 3,
    };

    // Mock data/calculations for Project Data tab
    const projectStatsData = {
        totalProjects: overviewData.ongoingProjects + overviewData.completedProjects,
        primaryRole: '编曲', // Example role - should ideally come from user profile
    };
    const averageCollaborators = projectStatsData.totalProjects > 0
        ? (overviewData.totalCollaborators / projectStatsData.totalProjects).toFixed(1)
        : 'N/A'; // Handle division by zero

    // Mock data for content engagement stats
    const contentStatsData = {
        totalFans: 1234,
        totalPlays: 56789,
        totalComments: 456,
        totalLikes: 7890,
        totalShares: 123,
        totalCollections: 345, // Or Bookmarks/Stars
    };

    return (
        <div className="container mx-auto px-4 py-8">
            {/* Add Banner at the top */}
            <CreatorBanner />

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Main Content Area */}
                <div className="lg:col-span-2 space-y-6">
                    <h1 className="text-3xl font-bold text-gray-900">创作中心</h1>

                    {/* Render Tabs */}
                    <Tabs
                        className="flex-shrink-0"
                        value={activeTab}
                        onChange={setActiveTab}
                    >
                        {[
                            <TabPanel key="overview" label="概览">
                                {/* Data Cards Grid */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                    <DataOverviewCard
                                        icon={ListTodo}
                                        title="进行中项目"
                                        value={overviewData.ongoingProjects}
                                    />
                                    <DataOverviewCard
                                        icon={FolderCheck}
                                        title="已完成项目"
                                        value={overviewData.completedProjects}
                                    />
                                    <DataOverviewCard
                                        icon={Users}
                                        title="总协作者"
                                        value={overviewData.totalCollaborators}
                                    />
                                    <DataOverviewCard
                                        icon={MailQuestion}
                                        title="待处理邀请"
                                        value={overviewData.pendingInvites}
                                    />
                                </div>
                            </TabPanel>,
                            <TabPanel key="project-data" label="项目数据">
                                {/* Render Project Data Cards - Use 3 columns on md+ */}
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                    <DataOverviewCard
                                        icon={FolderKanban}
                                        title="总项目数"
                                        value={projectStatsData.totalProjects}
                                    />
                                    <DataOverviewCard
                                        icon={UsersRound}
                                        title="平均协作者/项目"
                                        value={averageCollaborators}
                                    />
                                    <DataOverviewCard
                                        icon={UserCog}
                                        title="主要创作角色"
                                        value={projectStatsData.primaryRole}
                                    />
                                    {/* Add new engagement stat cards */}
                                    <DataOverviewCard
                                        icon={UserPlus}
                                        title="粉丝总数"
                                        value={contentStatsData.totalFans.toLocaleString()}
                                    />
                                    <DataOverviewCard
                                        icon={PlayCircle}
                                        title="总播放量"
                                        value={contentStatsData.totalPlays.toLocaleString()}
                                    />
                                    <DataOverviewCard
                                        icon={MessageCircle}
                                        title="总评论数"
                                        value={contentStatsData.totalComments.toLocaleString()}
                                    />
                                    <DataOverviewCard
                                        icon={ThumbsUp}
                                        title="总点赞数"
                                        value={contentStatsData.totalLikes.toLocaleString()}
                                    />
                                    <DataOverviewCard
                                        icon={Share2}
                                        title="总分享数"
                                        value={contentStatsData.totalShares.toLocaleString()}
                                    />
                                    <DataOverviewCard
                                        icon={Star}
                                        title="总收藏数"
                                        value={contentStatsData.totalCollections.toLocaleString()}
                                    />
                                </div>
                            </TabPanel>,
                            <TabPanel key="project-management" label="项目管理">
                                <div className="bg-white p-8 rounded-lg shadow-sm text-center text-gray-500 min-h-[200px] flex items-center justify-center">
                                    <p>项目管理功能正在开发中...</p>
                                    {/* Future content: List of projects, filters, actions */}
                                </div>
                            </TabPanel>,
                            <TabPanel key="comment-management" label="评论管理">
                                <div className="bg-white p-8 rounded-lg shadow-sm text-center text-gray-500 min-h-[200px] flex items-center justify-center">
                                    <p>评论管理功能正在开发中...</p>
                                    {/* Future content: List of comments, filters, actions */}
                                </div>
                            </TabPanel>,
                            <TabPanel key="revenue-management" label="收益管理">
                                <div className="bg-white p-8 rounded-lg shadow-sm text-center text-gray-500 min-h-[200px] flex items-center justify-center">
                                    <p>收益管理功能正在开发中...</p>
                                    {/* Future content: Revenue details, withdrawal options, history */}
                                </div>
                            </TabPanel>,
                            <TabPanel key="data-analysis" label="数据分析">
                                <div className="bg-white p-8 rounded-lg shadow-sm text-center text-gray-500 min-h-[200px] flex items-center justify-center">
                                    <p>数据分析功能正在开发中...</p>
                                    {/* Future content: Charts, analytics, insights */}
                                </div>
                            </TabPanel>,
                            <TabPanel key='creator-rights' label='创作权益'>
                                <div className="bg-white p-8 rounded-lg shadow-sm text-center text-gray-500 min-h-[200px] flex items-center justify-center">
                                    <p>创作权益功能正在开发中...</p>
                                    {/* Future content: User settings, preferences, notifications */}
                                </div>
                            </TabPanel>,
                            <TabPanel key="settings" label="设置">
                                <div className="bg-white p-8 rounded-lg shadow-sm text-center text-gray-500 min-h-[200px] flex items-center justify-center">
                                    <p>设置功能正在开发中...</p>
                                    {/* Future content: User settings, preferences, notifications */}
                                </div>
                            </TabPanel>
                        ]}
                        {/* Placeholder for future tabs */}
                        {/* 
                        <TabPanel label="项目管理">
                             <div className="bg-white p-4 rounded-lg shadow-sm">
                                <p>Project Management content placeholder</p>
                            </div>
                        </TabPanel>
                        <TabPanel label="数据分析">
                             <div className="bg-white p-4 rounded-lg shadow-sm">
                                <p>Analytics content placeholder</p>
                            </div>
                        </TabPanel>
                        */}
                    </Tabs>
                    {/* Recent Activity Section Placeholder */}
                    <div className="bg-white p-4 rounded-lg shadow-sm mt-6">
                        <h3 className="text-lg font-semibold text-gray-800 mb-3">近期项目动态</h3>
                        <div className="text-center text-gray-500 py-8">
                            (近期动态内容将在此处显示)
                        </div>
                    </div>
                </div>

                {/* Sidebar Area */}
                <div className="lg:col-span-1 space-y-6">
                    {/* Render Sidebar */}
                    <QuickActionsSidebar />
                </div>
            </div>
        </div>
    );
}; 