import React, { useState } from 'react';
import { UserProfile, ProgressiveIdentityStatus, ActivityRecord, PrivacySettings, CommunityPost, UserSummary, UserActivity } from '../types';
import { ProfileHeader } from '../components/ProfileHeader';
import { IdentityVerification } from '../components/IdentityVerification';
import { ActivityLog } from '../components/ActivityLog';
import { PrivacySettingsForm } from '../components/PrivacySettingsForm';
import { Tabs, TabPanel } from '../components/ui/Tabs';
import { TopUpSection } from '../components/TopUpSection';
import { UserActivityFeed } from '../components/Profile/UserActivityFeed';
import { UserCreatedSongsList } from '../components/Profile/UserCreatedSongsList';

// 导出模拟用户数据以便在 App.tsx 中使用
const mockUserPosts: CommunityPost[] = [
    { id: 'post1', title: '分享一下我的新歌Demo片段！', author: { id: 'currentUser123', name: '模拟演示用户', avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=DU' }, timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(), snippet: '最近在做一个流行风格的曲子...', commentCount: 5, likeCount: 15, link: '#' },
    { id: 'post2', title: '寻找合作编曲大神', author: { id: 'currentUser123', name: '模拟演示用户', avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=DU' }, timestamp: new Date(Date.now() - 1000 * 60 * 60 * 28).toISOString(), snippet: '有一首写好的歌...', commentCount: 12, likeCount: 25, link: '#' },
];
const mockFollowing: UserSummary[] = [
    { id: 'user3', name: '王五', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=WangWu' },
    { id: 'user4', name: '赵六', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ZhaoLiu' },
];
const mockFollowers: UserSummary[] = [
    { id: 'user1', name: '张三', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=ZhangSan' },
    { id: 'user2', name: '李四', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=LiSi' },
    { id: 'user5', name: '导演小明', avatar: 'https://api.dicebear.com/7.x/bottts/svg?seed=DirectorMing' },
];
const mockLikedSongIds = ['s1', 's6'];
const mockPurchasedSongIds = ['s3'];
const mockUserActivities: UserActivity[] = [
    { type: 'new_post' as const, postId: 'post1', postTitle: '分享一下我的新歌Demo片段！', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString() },
    { type: 'liked_song' as const, songId: 's6', songTitle: '街头节奏', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 8).toISOString() },
    { type: 'followed_user' as const, userId: 'user3', userName: '王五', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString() },
    { type: 'purchased_song' as const, songId: 's3', songTitle: '雨后初晴', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 26).toISOString() },
    { type: 'new_post' as const, postId: 'post2', postTitle: '寻找合作编曲大神', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 28).toISOString() },
    { type: 'liked_song' as const, songId: 's1', songTitle: '夏夜微风', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString() },
].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

export const mockUserProfile: UserProfile = {
    id: 'currentUser123',
    name: '模拟演示用户',
    email: '<EMAIL>',
    avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=DU',
    roles: ['singer', 'lyricist', 'composer'],
    bio: '热爱音乐创作的演示用户，擅长流行歌曲演唱和作词，也在学习作曲中。',
    skills: ['流行演唱', '作词', '人声录音', '基础乐理', '协作沟通'],
    points: 120,
    posts: mockUserPosts,
    following: mockFollowing,
    followers: mockFollowers,
    likedSongIds: mockLikedSongIds,
    purchasedSongIds: mockPurchasedSongIds,
    activityFeed: mockUserActivities,
    createdSongs: [
        {
            id: 'proj1',
            title: '夏日清凉 BGM',
            coverArt: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3wzNjUwNnx8fGVufDB8fHx8fA%3D%3D&ixlib=rb-4.0.3&q=80&w=400',
            artists: ['模拟演示用户', 'DJ Cool'],
            link: '/projects/proj1/workspace'
        },
        {
            id: 'proj2',
            title: '古风意境曲',
            coverArt: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3wzNjUwNnx8fGVufDB8fHx8fA%3D%3D&ixlib=rb-4.0.3&q=80&w=400',
            artists: ['王五', '模拟演示用户'],
            link: '/projects/proj2/workspace'
        },
        {
            id: 'proj3',
            title: '电影配乐片段 A',
            coverArt: 'https://images.unsplash.com/photo-1487180144351-b8472da7d491?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3wzNjUwNnx8fGVufDB8fHx8fA%3D%3D&ixlib=rb-4.0.3&q=80&w=400',
            artists: ['模拟演示用户'],
            link: '/projects/proj3/workspace'
        }
    ]
};

const mockIdentityStatus: ProgressiveIdentityStatus = {
    realName: 'unverified',
    professional: 'pending',
    musician: 'verified',
};

const mockActivityLog: ActivityRecord[] = [
    { id: 'act1', type: 'application_sent', projectName: '夏日清凉音乐节', projectId: 'proj1', status: 'pending', timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString() },
    { id: 'act2', type: 'invitation_received', projectName: '古风音乐创作', projectId: 'proj2', sourceUser: '王五', status: 'accepted', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString() },
    { id: 'act3', type: 'invitation_sent', projectName: '电影配乐片段征集', projectId: 'proj3', targetUser: '作曲大师', status: 'viewed', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString() },
];

const mockInitialPrivacySettings: PrivacySettings = {
    profileVisibility: 'public',
    projectVisibility: 'members_only',
};

export const Profile: React.FC = () => {
    const [privacySettings, setPrivacySettings] = useState<PrivacySettings>(mockInitialPrivacySettings);
    const [userPoints, setUserPoints] = useState(mockUserProfile.points);
    const [activeProfileTab, setActiveProfileTab] = useState<number>(0);

    const handleSavePrivacy = (newSettings: PrivacySettings) => {
        console.log("Saving privacy settings:", newSettings);
        setPrivacySettings(newSettings);
    };

    const handleTopUp = (amountInYuan: number) => {
        const pointsToAdd = amountInYuan * 10;
        const newBalance = userPoints + pointsToAdd;
        setUserPoints(newBalance);
        console.log(`Simulated top-up: ${amountInYuan} CNY -> ${pointsToAdd} points. New balance: ${newBalance}`);
        return true;
    };

    return (
        <div className="max-w-4xl mx-auto space-y-6">
            <ProfileHeader
                userProfile={mockUserProfile}
                currentPoints={userPoints}
                musicianStatus={mockIdentityStatus.musician}
            />

            <UserCreatedSongsList songs={mockUserProfile.createdSongs} />

            <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-100">
                <Tabs
                    value={activeProfileTab}
                    onChange={setActiveProfileTab}
                >
                    <TabPanel label="动态">
                        <UserActivityFeed activities={mockUserProfile.activityFeed} />
                    </TabPanel>
                    <TabPanel label="我的积分">
                        <TopUpSection currentPoints={userPoints} onTopUp={handleTopUp} />
                    </TabPanel>
                    <TabPanel label="身份认证">
                        <IdentityVerification status={mockIdentityStatus} />
                    </TabPanel>
                    <TabPanel label="活动记录">
                        <ActivityLog records={mockActivityLog} />
                    </TabPanel>
                    <TabPanel label="隐私设置">
                        <PrivacySettingsForm
                            initialSettings={privacySettings}
                            onSave={handleSavePrivacy}
                        />
                    </TabPanel>
                </Tabs>
            </div>
        </div>
    );
}; 