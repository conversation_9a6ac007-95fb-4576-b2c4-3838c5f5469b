import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Search, SlidersHorizontal } from 'lucide-react';
import { Project, ProjectStatus, ProjectSort, ProjectWorkspaceData } from '../types';
import { ProjectCard } from '../components/ProjectCard';
import { mockProjectsWorkspaceData } from '../data/mockProjects'; // 导入更新后的共享数据

// 不再需要内部的 mockProjects
// const mockProjects: Project[] = [...];

export const Projects: React.FC = () => {
    const navigate = useNavigate();
    const [status, setStatus] = useState<ProjectStatus>('all');
    const [sort, setSort] = useState<ProjectSort>('latest');
    const [search, setSearch] = useState('');

    // 过滤和排序项目 - 现在使用导入的 mockProjectsWorkspaceData
    const filteredProjects = mockProjectsWorkspaceData
        .filter((project: ProjectWorkspaceData) => {
            if (status !== 'all' && project.status !== status) return false;
            if (search && !project.name.toLowerCase().includes(search.toLowerCase())) return false;
            return true;
        })
        .sort((a: ProjectWorkspaceData, b: ProjectWorkspaceData) => {
            switch (sort) {
                case 'latest':
                    return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
                case 'progress':
                    return b.progress - a.progress;
                case 'popular':
                    return b.members.length - a.members.length;
                default:
                    return 0;
            }
        });

    const handleProjectClick = (projectId: string) => {
        navigate(`/projects/${projectId}`);
    };

    return (
        <div className="max-w-7xl mx-auto">
            {/* Header */}
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold text-gray-900">我的项目</h1>
                <button
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                    <Plus className="w-5 h-5 mr-2" />
                    创建项目
                </button>
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                        type="text"
                        placeholder="搜索项目..."
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                </div>
                <div className="flex gap-2">
                    <select
                        value={status}
                        onChange={(e) => setStatus(e.target.value as ProjectStatus)}
                        className="px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                        <option value="all">全部状态</option>
                        <option value="ongoing">进行中</option>
                        <option value="completed">已完成</option>
                    </select>
                    <select
                        value={sort}
                        onChange={(e) => setSort(e.target.value as ProjectSort)}
                        className="px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                        <option value="latest">最新更新</option>
                        <option value="popular">最多成员</option>
                        <option value="progress">完成进度</option>
                    </select>
                </div>
            </div>

            {/* Project Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredProjects.map((project: ProjectWorkspaceData) => (
                    <ProjectCard
                        key={project.id}
                        project={project}
                        onClick={() => handleProjectClick(project.id)}
                    />
                ))}
            </div>

            {/* Empty State */}
            {filteredProjects.length === 0 && (
                <div className="text-center py-12">
                    <p className="text-gray-500 mb-4">暂无符合条件的项目</p>
                    <button
                        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        <Plus className="w-5 h-5 mr-2" />
                        创建新项目
                    </button>
                </div>
            )}
        </div>
    );
}; 