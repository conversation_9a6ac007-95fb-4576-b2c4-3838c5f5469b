import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { CreatePost } from '../components/Community/CreatePost';
import { AuthState } from '../types';
import { ArrowLeft } from 'lucide-react';

interface CreatePostPageProps {
    auth: AuthState;
}

export const CreatePostPage: React.FC<CreatePostPageProps> = ({ auth }) => {
    const navigate = useNavigate();

    // Handler to navigate back after successful post
    const handlePostSuccess = () => {
        console.log('Post successful, navigating back to community.');
        navigate('/community');
    };

    // Redirect if not authenticated (basic check)
    if (!auth.isAuthenticated) {
        navigate('/login'); // Or show a login prompt
        return null; // Prevent rendering further
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="max-w-2xl mx-auto">
                {/* Back Button */}
                <Link to="/community" className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-4">
                    <ArrowLeft className="w-4 h-4 mr-1" />
                    返回社群
                </Link>

                {/* Page Title */}
                <h1 className="text-2xl font-bold text-gray-900 mb-6">发布新帖子</h1>

                {/* Render CreatePost component */}
                <CreatePost auth={auth} onPostSuccess={handlePostSuccess} />
            </div>
        </div>
    );
}; 