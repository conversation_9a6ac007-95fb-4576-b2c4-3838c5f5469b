import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ProjectDetailData, DiscussionComment, AuthState, UserRole, UploadedFile, ProjectWorkspaceData } from '../types'; // 导入 AuthState
import { ProjectOverview } from '../components/ProjectOverview';
import { UploadedContent } from '../components/UploadedContent';
import { DiscussionArea } from '../components/DiscussionArea';
import { JoinRequestButton } from '../components/JoinRequestButton';
import { CreateWorkspaceButton } from '../components/CreateWorkspaceButton'; // 导入新组件
import { Loader } from 'lucide-react';
import { mockProjectsWorkspaceData } from '../data/mockProjects'; // 导入更新后的共享数据

// 模拟 API 获取项目详情 - 使用共享数据
const fetchProjectDetail = (projectId: string): Promise<ProjectDetailData | null> => {
    console.log(`Fetching details for project: ${projectId} from shared data`);
    return new Promise((resolve) => {
        setTimeout(() => {
            // 直接在导入的共享数据中查找
            const mockProject = mockProjectsWorkspaceData.find((p: ProjectWorkspaceData) => p.id === projectId);

            if (mockProject) {
                resolve(mockProject);
            } else {
                resolve(null); // Not found
            }
        }, 300); // 减少模拟延迟
    });
};

interface ProjectDetailProps {
    auth: AuthState; // 接收 auth prop
}

export const ProjectDetail: React.FC<ProjectDetailProps> = ({ auth }) => { // 使用 auth prop
    const { projectId } = useParams<{ projectId: string }>();
    const navigate = useNavigate(); // 获取 navigate 函数
    const [project, setProject] = useState<ProjectDetailData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [comments, setComments] = useState<DiscussionComment[]>([]);

    useEffect(() => {
        if (projectId) {
            setLoading(true);
            setError(null);
            fetchProjectDetail(projectId)
                .then(data => {
                    if (data) {
                        setProject(data);
                        setComments(data.discussion);
                    } else {
                        setError('找不到该项目');
                    }
                })
                .catch(err => {
                    console.error("获取项目详情失败:", err);
                    setError('加载项目信息时出错');
                })
                .finally(() => setLoading(false));
        }
    }, [projectId]);

    const handlePostComment = (content: string) => {
        if (!project || !auth.user) return; // 使用传入的 auth
        const newComment: DiscussionComment = {
            id: `c${Date.now()}`,
            author: { name: auth.user.name, avatar: auth.user.avatar }, // 使用传入的 auth
            content: content,
            timestamp: new Date().toISOString(),
        };
        console.log("Posting comment:", newComment);
        setComments(prev => [...prev, newComment]);
    };

    // 处理创建协作空间按钮点击事件
    const handleCreateWorkspace = () => {
        if (!projectId) return;
        console.log(`为项目 ${projectId} 创建协作空间...`);
        // 导航到协作空间页面 (路由待定)
        navigate(`/projects/${projectId}/workspace`);
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <Loader className="w-10 h-10 animate-spin text-blue-600" />
                <p className="ml-3 text-gray-600">正在加载项目详情...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center py-10 text-red-600">
                {error}
            </div>
        );
    }

    if (!project) {
        return <div className="text-center py-10 text-gray-500">项目未找到。</div>;
    }

    return (
        <div className="max-w-4xl mx-auto">
            <ProjectOverview project={project} />
            <UploadedContent files={project.uploadedFiles} />
            <DiscussionArea
                comments={comments}
                auth={auth}
                onPostComment={handlePostComment}
            />
            {/* 操作按钮区域 */}
            <div className="mt-6 flex flex-col sm:flex-row gap-4 items-start">
                <JoinRequestButton project={project} auth={auth} />
                <CreateWorkspaceButton project={project} auth={auth} onClick={handleCreateWorkspace} />
            </div>
        </div>
    );
}; 