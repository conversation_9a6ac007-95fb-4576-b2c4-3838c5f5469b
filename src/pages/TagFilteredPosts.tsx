import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { CommunityPost, AuthState } from '../types';
import { ArrowLeft, MessageSquare, ThumbsUp, Share2, Bookmark } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

// --- Temporary Mock Data (Copied from PostList) ---
// In a real app, fetch this data or get from state management
const allMockPosts: CommunityPost[] = [
    {
        id: 'p1',
        title: '求助：如何让我的混音更有空间感？',
        author: { id: 'u1', name: '混音小白', avatar: 'https://api.dicebear.com/7.x/fun-emoji/svg?seed=Felix' },
        timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
        snippet: '我尝试了各种混响和延迟，但总感觉声音挤在一起，不够开阔...',
        commentCount: 8,
        likeCount: 15,
        link: '/community/post/p1',
        tags: ['#混音', '#空间感', '#求助']
    },
    {
        id: 'p2',
        title: '分享一个我刚完成的 Lo-Fi HipHop Beat',
        author: { id: 'u2', name: '节奏玩家', avatar: 'https://api.dicebear.com/7.x/shapes/svg?seed=Jasper' },
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        snippet: '欢迎大家听听给点意见！使用了 SP404 和 Ableton Live 制作。',
        commentCount: 12,
        likeCount: 35,
        link: '/community/post/p2',
        tags: ['#分享', '#LoFi', '#HipHop', '#Beatmaking']
    },
    {
        id: 'p3',
        title: '讨论：AI 在音乐创作中的应用前景？',
        author: { id: 'u3', name: '科技探索者', avatar: 'https://api.dicebear.com/7.x/identicon/svg?seed=Bandit' },
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
        snippet: '最近试用了几个 AI 作曲工具，感觉很神奇，但也有些担忧...',
        commentCount: 25,
        likeCount: 48,
        link: '/community/post/p3',
        tags: ['#讨论', '#AI', '#音乐创作', '#未来']
    },
    {
        id: 'p4', // Adding another post for variety
        title: '新手编曲入门心得分享',
        author: { id: 'u4', name: '编曲萌新', avatar: 'https://api.dicebear.com/7.x/pixel-art/svg?seed=Lucy' },
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(),
        snippet: '刚开始学编曲一个月，分享一些踩过的坑和有用的资源...',
        commentCount: 5,
        likeCount: 22,
        link: '/community/post/p4',
        tags: ['#分享', '#编曲', '#新手', '#教程', '#音乐创作']
    }
];
// --- End Temporary Mock Data ---

interface TagFilteredPostsProps {
    auth: AuthState;
}

export const TagFilteredPosts: React.FC<TagFilteredPostsProps> = ({ auth }) => {
    const { tagName } = useParams<{ tagName: string }>();
    const [filteredPosts, setFilteredPosts] = useState<CommunityPost[]>([]);
    const [loading, setLoading] = useState(true);

    // --- Debugging Logs --- 
    console.log("[TagFilteredPosts] Raw tagName from URL:", tagName);
    const decodedTagName = tagName ? decodeURIComponent(tagName) : '';
    const tagWithHash = `#${decodedTagName}`;
    console.log("[TagFilteredPosts] Decoded TagName:", decodedTagName);
    console.log("[TagFilteredPosts] Tag used for filtering (tagWithHash):", tagWithHash);
    // --- End Debugging Logs ---

    useEffect(() => {
        console.log("[TagFilteredPosts] useEffect triggered for tag:", tagWithHash);
        setLoading(true);
        const timer = setTimeout(() => {
            console.log("[TagFilteredPosts] Filtering mock data...");
            const posts = allMockPosts.filter(post =>
                post.tags?.includes(tagWithHash)
            );
            // --- Debugging Log ---
            console.log("[TagFilteredPosts] Filtered posts result:", posts);
            // --- End Debugging Log ---
            setFilteredPosts(posts);
            setLoading(false);
        }, 300);

        return () => clearTimeout(timer);
    }, [tagWithHash]);

    // --- State for interaction (like/collect) - Simplified local state for demo ---
    const [likedPosts, setLikedPosts] = useState<Set<string>>(new Set());
    const [collectedPosts, setCollectedPosts] = useState<Set<string>>(new Set());

    const handleLike = (postId: string) => {
        if (!auth.isAuthenticated) return;
        setLikedPosts(prev => {
            const newSet = new Set(prev);
            if (newSet.has(postId)) newSet.delete(postId); else newSet.add(postId);
            return newSet;
        });
        // Note: This won't update the likeCount displayed as it reads from original mock data
    };

    const handleCollect = (postId: string) => {
        if (!auth.isAuthenticated) return;
        setCollectedPosts(prev => {
            const newSet = new Set(prev);
            if (newSet.has(postId)) newSet.delete(postId); else newSet.add(postId);
            return newSet;
        });
    };

    const handleShare = (postId: string) => {
        const postLink = `${window.location.origin}/community/post/${postId}`;
        navigator.clipboard.writeText(postLink);
        alert('帖子链接已复制到剪贴板！');
    };
    // --- End Interaction State ---

    // --- Debugging Log ---
    console.log("[TagFilteredPosts] State before render - loading:", loading, "filteredPosts length:", filteredPosts.length);
    // --- End Debugging Log ---

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="max-w-3xl mx-auto">
                {/* Back Button and Title */}
                <div className="mb-6">
                    <Link to="/community" className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-2">
                        <ArrowLeft className="w-4 h-4 mr-1" />
                        返回社群
                    </Link>
                    <h1 className="text-2xl font-bold text-gray-900">标签: <span className="text-blue-600">{tagWithHash}</span></h1>
                </div>

                {/* Post List */}
                {loading ? (
                    <div className="text-center py-10">加载中...</div>
                ) : (
                    filteredPosts.length > 0 ? (
                        <div className="space-y-6">
                            {filteredPosts.map(post => (
                                <div key={post.id} className="bg-white rounded-xl shadow-sm p-6">
                                    <div className="flex items-start space-x-3">
                                        <img src={post.author.avatar} alt={post.author.name} className="w-10 h-10 rounded-full bg-gray-100 mt-1" />
                                        <div className="flex-1">
                                            <Link to={`/community/post/${post.id}`} className="hover:text-blue-600 transition-colors">
                                                <h3 className="font-semibold text-gray-800 mb-1 leading-snug">{post.title}</h3>
                                            </Link>
                                            <p className="text-sm text-gray-600 mb-3 line-clamp-2">{post.snippet}</p>
                                            {post.tags && post.tags.length > 0 && (
                                                <div className="flex flex-wrap gap-2 mb-3">
                                                    {post.tags.map((tag, index) => (
                                                        <Link
                                                            key={index}
                                                            to={`/community/tags/${encodeURIComponent(tag.substring(1))}`}
                                                            className="px-2 py-0.5 rounded-full text-xs bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors"
                                                        >
                                                            {tag}
                                                        </Link>
                                                    ))}
                                                </div>
                                            )}
                                            <div className="flex items-center justify-between text-xs text-gray-500">
                                                <span>{post.author.name} • {formatDistanceToNow(new Date(post.timestamp), { addSuffix: true, locale: zhCN })}</span>
                                                <div className="flex items-center space-x-4">
                                                    <button onClick={() => handleLike(post.id)} className="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors"><ThumbsUp className="w-4 h-4" /><span>{post.likeCount}</span></button>
                                                    <Link to={`/community/post/${post.id}`} className="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors"><MessageSquare className="w-4 h-4" /><span>{post.commentCount}</span></Link>
                                                    <button onClick={() => handleCollect(post.id)} className="flex items-center space-x-1 text-gray-500 hover:text-yellow-600 transition-colors"><Bookmark className="w-4 h-4" /></button>
                                                    <button onClick={() => handleShare(post.id)} className="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors"><Share2 className="w-4 h-4" /></button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-10 text-gray-500">
                            没有找到包含标签 "{tagWithHash}" 的帖子。
                        </div>
                    )
                )}
            </div>
        </div>
    )
}
