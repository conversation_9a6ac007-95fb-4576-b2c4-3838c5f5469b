export type UserRole = 'lyricist' | 'composer' | 'singer' | 'arranger' | 'mixer' | 'producer';

export interface User {
  id: string;
  roles: UserRole[];
  name: string;
  avatar: string;
  email: string;
}

// Summary type for lists of users
export type UserSummary = Pick<User, 'id' | 'name' | 'avatar'>;

export interface UserProfile extends User {
  bio?: string;
  representativeWorks?: string[];
  skills?: string[];
  points: number;
  posts: CommunityPost[];
  following: UserSummary[];
  followers: UserSummary[];
  likedSongIds: string[];
  purchasedSongIds: string[];
  activityFeed: UserActivity[];
  createdSongs?: CreatedSong[];
}

export interface AuthState {
  isAuthenticated: boolean;
  user: UserProfile | null;
}

export interface NavItem {
  label: string;
  path: string;
  icon: React.ComponentType;
}

export type MusicStyle = 'pop' | 'rock' | 'folk' | 'electronic' | 'hiphop' | 'classical' | 'jazz';

export interface Project {
  id: string;
  name: string;
  description: string;
  progress: number;
  status: 'ongoing' | 'completed';
  members: {
    id: string;
    name: string;
    avatar: string;
  }[];
  createdAt: string;
  updatedAt: string;
  tags: string[];
}

export type ProjectStatus = 'all' | 'ongoing' | 'completed';
export type ProjectSort = 'latest' | 'popular' | 'progress';

export interface ProjectFormData {
  title: string;
  description: string;
  style: MusicStyle;
  neededRoles: UserRole[];
  completedRoles: UserRole[];
  demoUrl?: string;
}

export interface HotSong {
  id: string;
  title: string;
  artists: string[]; // 可以是多个艺术家 (保留作为显示，创作者是核心)
  coverArt: string;
  plays: number; // 播放次数或其他热度指标
  genre: string;
  projectId: string; // Link back to the source project
  creators: UserSummary[]; // Use UserSummary
  supportCount: number; // Number of supports/likes
  likeCount: number; // Add like count
}

// Define Post Categories
export const POST_CATEGORIES = ['提问', '分享', '教程', '讨论', '作品展示', '其他'] as const;
export type PostCategory = typeof POST_CATEGORIES[number];

// Define Post Visibility Options
export type PostVisibility = 'public' | 'followers';

export interface CommunityPost {
  id: string;
  title: string;
  author: UserSummary;
  timestamp: string;
  snippet: string;
  content?: string;
  tags?: string[];
  category?: PostCategory;
  visibility?: PostVisibility; // Add visibility field
  commentCount: number;
  likeCount: number;
  link: string;
}

// Type for file comments
export interface FileComment {
  id: string;
  author: UserSummary; // Reuse UserSummary for author info
  timestamp: string; // ISO date string
  text: string;
}

// Type for Folders in FileArea
export interface FileFolder {
  id: string;
  name: string;
  projectId: string; // Link folder to a project
}

// Represents an uploaded file within a project workspace
export interface UploadedFile {
  id: string;
  name: string;
  type: 'audio' | 'video' | 'image' | 'document' | 'score' | 'midi' | 'other';
  uploadedAt: string; // ISO date string
  uploader: UserSummary;
  fileUrl: string; // URL to download/access the file
  folderId?: string; // Optional: ID of the folder it belongs to
  comments?: FileComment[]; // Optional: Array of comments on the file
  // Optional: Array of previous versions for simple history
  previousVersions?: {
    uploadedAt: string;
    fileUrl: string;
    uploader: UserSummary;
    // Optional: could add version number or description later
  }[];
}

export interface DiscussionComment {
  id: string;
  author: UserSummary; // Use UserSummary
  content: string;
  timestamp: string;
  parentId?: string; // ID of the comment being replied to
}

export interface ProjectDetailData extends Project {
  creator: UserSummary; // Use UserSummary
  members: UserSummary[]; // Use UserSummary (if not already)
  neededRoles: {
    role: UserRole;
    filled: boolean; // Is this role already filled?
  }[];
  uploadedFiles: UploadedFile[];
  discussion: DiscussionComment[];
}

// Type for unified user activity feed
export type UserActivity =
  | { type: 'new_post'; postId: string; postTitle: string; timestamp: string }
  | { type: 'liked_song'; songId: string; songTitle: string; timestamp: string }
  | { type: 'purchased_song'; songId: string; songTitle: string; timestamp: string }
  | { type: 'followed_user'; userId: string; userName: string; timestamp: string };

export type VerificationStatus = 'unverified' | 'pending' | 'verified';

export interface ProgressiveIdentityStatus {
  realName: VerificationStatus;
  professional: VerificationStatus;
  musician: VerificationStatus;
}

export type ActivityRecordType = 'invitation_sent' | 'invitation_received' | 'application_sent' | 'application_received';
export type ActivityRecordStatus = 'pending' | 'accepted' | 'rejected' | 'viewed';

export interface ActivityRecord {
  id: string;
  type: ActivityRecordType;
  projectName: string;
  projectId: string;
  targetUser?: string; // For sent invitations/applications
  sourceUser?: string; // For received invitations/applications
  status: ActivityRecordStatus;
  timestamp: string;
}

export type VisibilitySetting = 'public' | 'members_only' | 'private';

export interface PrivacySettings {
  profileVisibility: VisibilitySetting;
  projectVisibility: VisibilitySetting; // Default visibility for new projects
}

export type SystemNotificationType =
  | 'project_update'
  | 'application_status'
  | 'invitation'
  | 'new_comment'
  | 'system_announcement';

export interface SystemNotification {
  id: string;
  type: SystemNotificationType;
  title: string;
  content: string;
  timestamp: string;
  isRead: boolean;
  link?: string; // Optional link to related content
}

export interface ChatMessage {
  id: string;
  conversationId: string;
  senderId: string; // ID of the user who sent the message
  senderName: string; // Cache sender name for display
  senderAvatar: string; // Cache sender avatar for display
  content: string;
  timestamp: string;
  isRead?: boolean; // Optional read status
}

export interface ChatConversation {
  id: string;
  participants: UserSummary[]; // Use UserSummary
  lastMessage: Pick<ChatMessage, 'content' | 'timestamp' | 'senderName'> | null;
  unreadCount: number;
}

// ---------- Project Workspace Types ----------

export interface TimelineEvent {
  id: string;
  title: string;
  date: string; // Could be a specific date or a range
  status: 'completed' | 'ongoing' | 'planned';
  description?: string;
  icon?: React.ReactNode; // Optional icon for the timeline marker
}

// Combines project details with workspace-specific data
export interface ProjectWorkspaceData extends ProjectDetailData {
  creator: UserSummary;
  members: UserSummary[];
  timeline: TimelineEvent[];
}

// Type for Global Hot Songs ranking
export interface GlobalHotSong {
  id: string;
  rank: number;
  title: string;
  artists: string[];
  coverArt: string;
  heatScore: number; // Example metric for ranking
}

// Type for Hot Community Activities
export interface HotActivity {
  id: string;
  title: string;
  imageUrl: string;
  link: string;
  description?: string;
  tag?: string;
}

// Type for Live Streaming Info
export interface LiveStreamInfo {
  id: string;
  streamerName: string;
  streamerAvatar: string;
  streamTitle: string;
  streamLink: string;
}

// Type for Creator Center Data Overview Card
export interface DataOverview {
  ongoingProjects: number;
  completedProjects: number;
  totalCollaborators: number;
  pendingInvites: number;
}

// Type for a song created by the user (for Profile page)
export interface CreatedSong {
  id: string; // Can be project ID or unique song ID
  title: string;
  coverArt: string;
  artists: string[]; // Main artists involved
  link: string; // Link to project workspace or song detail
}