import React, { useState } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { Navigation } from './components/Navigation';
import { Registration } from './components/Registration';
import { CollaborationSquare } from './pages/CollaborationSquare';
import { Home } from './pages/Home';
import { Projects } from './pages/Projects';
import { ProjectDetail } from './pages/ProjectDetail';
import { ProjectWorkspace } from './pages/ProjectWorkspace';
import { Profile } from './pages/Profile';
import { MessageCenter } from './pages/MessageCenter';
import { UserRole, AuthState, UserProfile } from './types';
import { mockUserProfile } from './pages/Profile'; // 导入模拟用户数据
import { Community } from './pages/Community';
import { PostDetail } from './pages/PostDetail';
import { TagFilteredPosts } from './pages/TagFilteredPosts';
import { CreatePostPage } from './pages/CreatePostPage';
import { CreatorCenterPage } from './pages/CreatorCenterPage';

function App() {
  const [hasSelectedRoles, setHasSelectedRoles] = useState(false);
  const [auth, setAuth] = useState<AuthState>({
    isAuthenticated: false,
    user: null
  });

  const handleLogin = () => {
    // 使用 Profile 页面的模拟用户数据进行登录
    setAuth({
      isAuthenticated: true,
      user: mockUserProfile
    });
    setHasSelectedRoles(true); // Assume login implies roles are somehow selected/known
  };

  const handleRoleSelection = (roles: UserRole[]) => {
    console.log('Selected roles (will be used for demo user login):', roles);
    setHasSelectedRoles(true);
    handleLogin(); // Login as demo user immediately after role selection
  };

  const handleLogout = () => {
    setAuth({ isAuthenticated: false, user: null });
    // setHasSelectedRoles(false); // Don't reset this, let them stay on page or get redirected
    console.log('User logged out');
    // No forced navigation needed here
  };

  return (
    <BrowserRouter>
      <div className="min-h-screen bg-gray-50">
        <Navigation auth={auth} onLogout={handleLogout} />
        <main className="pb-20 md:pb-6 md:pt-20 p-4 md:p-6">
          <Routes>
            <Route path="/" element={<Home auth={auth} />} />
            <Route path="/collaboration" element={<CollaborationSquare />} />
            <Route
              path="/projects"
              element={
                auth.isAuthenticated ? (
                  <Projects />
                ) : (
                  <Navigate to="/login" replace />
                )
              }
            />
            <Route
              path="/projects/:projectId"
              element={
                auth.isAuthenticated ? (
                  <ProjectDetail auth={auth} />
                ) : (
                  <Navigate to="/login" replace />
                )
              }
            />
            <Route
              path="/projects/:projectId/workspace"
              element={
                auth.isAuthenticated ? (
                  <ProjectWorkspace auth={auth} />
                ) : (
                  <Navigate to="/login" replace />
                )
              }
            />
            <Route
              path="/messages"
              element={
                auth.isAuthenticated ? (
                  <MessageCenter auth={auth} />
                ) : (
                  <Navigate to="/login" replace />
                )
              }
            />
            <Route
              path="/profile"
              element={
                auth.isAuthenticated ? (
                  <Profile />
                ) : (
                  <Navigate to="/login" replace />
                )
              }
            />
            <Route
              path="/login"
              element={
                !auth.isAuthenticated ? (
                  <div className="flex flex-col items-center justify-center min-h-[60vh]">
                    <h1 className="text-2xl font-bold mb-4">登录</h1>
                    <button
                      onClick={handleLogin}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      模拟登录 (切换为演示用户)
                    </button>
                  </div>
                ) : (
                  <Navigate to="/profile" replace />
                )
              }
            />
            <Route
              path="/community"
              element={<Community auth={auth} />}
            />
            <Route
              path="/community/post/:postId"
              element={<PostDetail auth={auth} />}
            />
            <Route
              path="/community/tags/:tagName"
              element={<TagFilteredPosts auth={auth} />}
            />
            <Route
              path="/community/create"
              element={<CreatePostPage auth={auth} />}
            />
            <Route path="/messages/new/:userId" element={<MessageCenter auth={auth} />} />
            <Route
              path="/creator-center"
              element={
                auth.isAuthenticated ? (
                  <CreatorCenterPage />
                ) : (
                  <Navigate to="/login" replace />
                )
              }
            />
            <Route
              path="/register"
              element={
                !auth.isAuthenticated ? (
                  <Registration onComplete={handleRoleSelection} />
                ) : (
                  <Navigate to="/" replace />
                )
              }
            />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </main>
      </div>
    </BrowserRouter>
  );
}

export default App;