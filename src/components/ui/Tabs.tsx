import React from 'react';

// Make Tabs a controlled component
interface TabsProps {
    children: React.ReactNode[];
    className?: string;
    value: number; // Index of the active tab
    onChange: (index: number) => void; // Callback when tab changes
}

export const Tabs: React.FC<TabsProps> = ({ children, className, value: activeIndex, onChange }) => {
    // Extract labels from children
    const tabs = React.Children.map(children, (child) => {
        if (React.isValidElement<{ label: string }>(child)) {
            return child.props.label;
        }
        return null;
    });

    const handleTabClick = (index: number) => {
        if (typeof onChange === 'function') {
            onChange(index);
        } else {
            console.error('[Tabs component] onChange prop is not a function. Received:', onChange);
        }
    };

    return (
        <div className={className}>
            {/* Tab Navigation */}
            <div className="border-b border-gray-200 mb-4">
                <nav className="-mb-px flex space-x-6 overflow-x-auto" aria-label="Tabs">
                    {tabs?.map((label, index) => label && (
                        <button
                            key={label}
                            onClick={() => handleTabClick(index)}
                            className={`whitespace-nowrap py-3 px-2 border-b-2 font-medium text-sm focus:outline-none ${activeIndex === index
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                        >
                            {label}
                        </button>
                    ))}
                </nav>
            </div>
            {/* Tab Content */}
            <div>
                {/* Render only the active tab panel based on the passed value */}
                {React.Children.toArray(children)[activeIndex]}
            </div>
        </div>
    );
};

// TabPanel component: Its main purpose is to provide a label and contain content.
// The actual rendering logic is handled by the Tabs component.
export const TabPanel: React.FC<{ label: string; children: React.ReactNode }> = ({ children }) => {
    return <>{children}</>;
}; 