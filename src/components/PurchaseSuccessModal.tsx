import React, { useEffect, useRef, useState } from 'react';
import Confetti from 'react-confetti';
import { X, CheckCircle } from 'lucide-react';

interface PurchaseSuccessModalProps {
    isOpen: boolean;
    onClose: () => void;
    songTitle: string;
}

export const PurchaseSuccessModal: React.FC<PurchaseSuccessModalProps> = ({ isOpen, onClose, songTitle }) => {
    const modalContentRef = useRef<HTMLDivElement>(null);
    const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

    // Get modal dimensions for confetti canvas
    useEffect(() => {
        if (isOpen && modalContentRef.current) {
            setDimensions({
                width: modalContentRef.current.offsetWidth,
                height: modalContentRef.current.offsetHeight,
            });
        }
    }, [isOpen]);

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60 backdrop-blur-sm animate-fade-in">
            {/* Modal Content Box */}
            <div
                ref={modalContentRef}
                className="relative bg-white rounded-xl shadow-2xl p-6 sm:p-8 w-11/12 max-w-md text-center transform transition-all"
            >
                {/* Close Button */}
                <button
                    onClick={onClose}
                    className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 transition-colors"
                    aria-label="关闭"
                >
                    <X size={24} />
                </button>

                {/* Confetti Canvas - position absolute relative to this box */}
                {isOpen && dimensions.width > 0 && (
                    <Confetti
                        recycle={false}
                        numberOfPieces={150}
                        width={dimensions.width}
                        height={dimensions.height}
                        gravity={0.1}
                        initialVelocityY={15}
                        style={{ position: 'absolute', top: 0, left: 0, zIndex: 0 }} // Behind content
                    />
                )}

                {/* Content (ensure it's above confetti with z-index or structure) */}
                <div className="relative z-10">
                    <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                        <CheckCircle className="h-10 w-10 text-green-600" aria-hidden="true" />
                    </div>
                    <h3 className="text-xl font-semibold leading-6 text-gray-900 mb-2" id="modal-title">
                        购买成功!
                    </h3>
                    <div className="mt-2">
                        <p className="text-sm text-gray-600">
                            感谢您购买支持歌曲 <span className="font-medium">{songTitle}</span>！
                        </p>
                    </div>
                    <div className="mt-6">
                        <button
                            type="button"
                            className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                            onClick={onClose}
                        >
                            太棒了!
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

// Add animations to global CSS or tailwind.config.js if needed
/*
@keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
.animate-fade-in { animation: fadeIn 0.3s ease-out forwards; }

@keyframes scaleIn { 
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}
.animate-scale-in { animation: scaleIn 0.3s ease-out forwards; }
*/ 