import React, { useState } from 'react';
import { X } from 'lucide-react';
import { ProjectFormData, MusicStyle, UserRole } from '../../types';

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ProjectFormData) => void;
}

export const CreateProjectModal: React.FC<Props> = ({ isOpen, onClose, onSubmit }) => {
  const [formData, setFormData] = useState<ProjectFormData>({
    title: '',
    description: '',
    style: 'pop',
    neededRoles: [],
    completedRoles: [],
  });

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
    onClose();
  };

  const toggleRole = (role: UserRole, type: 'needed' | 'completed') => {
    const key = type === 'needed' ? 'neededRoles' : 'completedRoles';
    setFormData(prev => ({
      ...prev,
      [key]: prev[key].includes(role)
        ? prev[key].filter(r => r !== role)
        : [...prev[key], role]
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-2xl font-semibold">发起新项目</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              项目名称
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={e => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              项目描述
            </label>
            <textarea
              value={formData.description}
              onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows={4}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              音乐风格
            </label>
            <select
              value={formData.style}
              onChange={e => setFormData(prev => ({ ...prev, style: e.target.value as MusicStyle }))}
              className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="pop">流行</option>
              <option value="rock">摇滚</option>
              <option value="folk">民谣</option>
              <option value="electronic">电子</option>
              <option value="hiphop">说唱</option>
              <option value="classical">古典</option>
              <option value="jazz">爵士</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              已完成的部分
            </label>
            <div className="flex flex-wrap gap-2">
              {['lyricist', 'composer', 'singer', 'arranger', 'mixer', 'producer'].map((role) => (
                <button
                  key={role}
                  type="button"
                  onClick={() => toggleRole(role as UserRole, 'completed')}
                  className={`px-4 py-2 rounded-lg text-sm transition-colors
                    ${formData.completedRoles.includes(role as UserRole)
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                >
                  {role === 'lyricist' && '作词'}
                  {role === 'composer' && '作曲'}
                  {role === 'singer' && '演唱'}
                  {role === 'arranger' && '编曲'}
                  {role === 'mixer' && '混音'}
                  {role === 'producer' && '制作'}
                </button>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              需要的角色
            </label>
            <div className="flex flex-wrap gap-2">
              {['lyricist', 'composer', 'singer', 'arranger', 'mixer', 'producer'].map((role) => (
                <button
                  key={role}
                  type="button"
                  onClick={() => toggleRole(role as UserRole, 'needed')}
                  className={`px-4 py-2 rounded-lg text-sm transition-colors
                    ${formData.neededRoles.includes(role as UserRole)
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                >
                  {role === 'lyricist' && '作词'}
                  {role === 'composer' && '作曲'}
                  {role === 'singer' && '演唱'}
                  {role === 'arranger' && '编曲'}
                  {role === 'mixer' && '混音'}
                  {role === 'producer' && '制作'}
                </button>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Demo链接（可选）
            </label>
            <input
              type="url"
              value={formData.demoUrl || ''}
              onChange={e => setFormData(prev => ({ ...prev, demoUrl: e.target.value }))}
              className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="https://"
            />
          </div>

          <div className="flex justify-end pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 text-gray-600 hover:text-gray-800 mr-4"
            >
              取消
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              发布项目
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};