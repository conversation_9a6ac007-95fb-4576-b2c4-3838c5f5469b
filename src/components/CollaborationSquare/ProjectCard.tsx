import React from 'react';
import { ArrowR<PERSON>, Eye, Users } from 'lucide-react';
import { Project, UserRole } from '../../types';

interface Props {
  project: Project;
  onViewDetails: (projectId: string) => void;
}

const roleLabels: Record<UserRole, string> = {
  lyricist: '作词',
  composer: '作曲',
  singer: '演唱',
  arranger: '编曲',
  mixer: '混音',
  producer: '制作',
};

export const ProjectCard: React.FC<Props> = ({ project, onViewDetails }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6">
      {project.coverImage && (
        <div className="h-48 mb-4 rounded-lg overflow-hidden">
          <img
            src={project.coverImage}
            alt={project.title}
            className="w-full h-full object-cover"
          />
        </div>
      )}
      <h3 className="text-xl font-semibold mb-2">{project.title}</h3>
      <div className="flex items-center text-sm text-gray-600 mb-4">
        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
          {roleLabels[project.creatorRole]}
        </span>
      </div>
      
      <div className="space-y-3 mb-4">
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-1">已完成</h4>
          <div className="flex flex-wrap gap-2">
            {project.progress.completed.map(role => (
              <span
                key={role}
                className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded"
              >
                {roleLabels[role]}
              </span>
            ))}
          </div>
        </div>
        
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-1">正在寻找</h4>
          <div className="flex flex-wrap gap-2">
            {project.progress.needed.map(role => (
              <span
                key={role}
                className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded"
              >
                {roleLabels[role]}
              </span>
            ))}
          </div>
        </div>
      </div>

      <div className="flex items-center justify-between pt-4 border-t">
        <div className="flex items-center space-x-4 text-sm text-gray-500">
          <span className="flex items-center">
            <Users className="w-4 h-4 mr-1" />
            {project.participants}
          </span>
          <span className="flex items-center">
            <Eye className="w-4 h-4 mr-1" />
            {project.views}
          </span>
        </div>
        
        <button
          onClick={() => onViewDetails(project.id)}
          className="flex items-center text-blue-600 hover:text-blue-800 transition-colors"
        >
          查看详情
          <ArrowRight className="w-4 h-4 ml-1" />
        </button>
      </div>
    </div>
  );
};