import React from 'react';
import { MusicStyle, UserRole } from '../../types';

interface Props {
  selectedRole: UserRole | null;
  selectedStyle: MusicStyle | null;
  onRoleChange: (role: UserRole | null) => void;
  onStyleChange: (style: MusicStyle | null) => void;
}

const roles: { value: UserRole; label: string }[] = [
  { value: 'lyricist', label: '作词人' },
  { value: 'composer', label: '作曲人' },
  { value: 'singer', label: '歌手' },
  { value: 'arranger', label: '编曲' },
  { value: 'mixer', label: '混音' },
  { value: 'producer', label: '制作人' },
];

const styles: { value: MusicStyle; label: string }[] = [
  { value: 'pop', label: '流行' },
  { value: 'rock', label: '摇滚' },
  { value: 'folk', label: '民谣' },
  { value: 'electronic', label: '电子' },
  { value: 'hiphop', label: '说唱' },
  { value: 'classical', label: '古典' },
  { value: 'jazz', label: '爵士' },
];

export const ProjectFilters: React.FC<Props> = ({
  selectedRole,
  selectedStyle,
  onRoleChange,
  onStyleChange,
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-4 space-y-4">
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-2">按需求角色筛选</h3>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => onRoleChange(null)}
            className={`px-3 py-1 rounded-full text-sm transition-colors
              ${!selectedRole
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
          >
            全部
          </button>
          {roles.map(({ value, label }) => (
            <button
              key={value}
              onClick={() => onRoleChange(value)}
              className={`px-3 py-1 rounded-full text-sm transition-colors
                ${selectedRole === value
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-2">按音乐风格筛选</h3>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => onStyleChange(null)}
            className={`px-3 py-1 rounded-full text-sm transition-colors
              ${!selectedStyle
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
          >
            全部
          </button>
          {styles.map(({ value, label }) => (
            <button
              key={value}
              onClick={() => onStyleChange(value)}
              className={`px-3 py-1 rounded-full text-sm transition-colors
                ${selectedStyle === value
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};