import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Music } from 'lucide-react';
import { mockGlobalHotSongsData } from '../data/mockGlobalHotSongs.ts'; // Corrected path
import { GlobalHotSong } from '../types';

export const GlobalHotSongsList: React.FC = () => {
    const [isExpanded, setIsExpanded] = useState(false);

    const displayedSongs = isExpanded ? mockGlobalHotSongsData : mockGlobalHotSongsData.slice(0, 10);

    return (
        <div className="mt-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                <Music className="w-6 h-6 mr-2 text-red-500" />
                全球热歌榜 Top 50
            </h2>
            <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <ul className="divide-y divide-gray-200">
                    {displayedSongs.map((song) => (
                        <li key={song.id} className="px-4 py-3 sm:px-6 flex items-center space-x-4 hover:bg-gray-50 transition-colors">
                            <span className="text-base font-medium text-gray-500 w-8 text-right flex-shrink-0">{song.rank}.</span>
                            <img src={song.coverArt} alt={song.title} className="w-12 h-12 rounded object-cover flex-shrink-0" />
                            <div className="flex-grow min-w-0">
                                <p className="font-medium text-gray-900 truncate text-sm sm:text-base">{song.title}</p>
                                <p className="text-sm text-gray-500 truncate">{song.artists.join(', ')}</p>
                            </div>
                            <div className="ml-auto text-right flex-shrink-0 pl-2">
                                <p className="text-sm sm:text-base font-semibold text-red-600">{song.heatScore.toLocaleString()}</p>
                                <p className="text-xs text-gray-400">热度</p>
                            </div>
                        </li>
                    ))}
                </ul>
            </div>
            {mockGlobalHotSongsData.length > 10 && (
                <div className="mt-4 text-center">
                    <button
                        onClick={() => setIsExpanded(!isExpanded)}
                        className="text-sm text-blue-600 hover:text-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 px-4 py-2 rounded-md inline-flex items-center transition-colors"
                    >
                        {isExpanded ? (
                            <>
                                <ChevronUp className="w-4 h-4 mr-1" />
                                收起部分榜单
                            </>
                        ) : (
                            <>
                                <ChevronDown className="w-4 h-4 mr-1" />
                                查看完整榜单 Top 50
                            </>
                        )}
                    </button>
                </div>
            )}
        </div>
    );
}; 