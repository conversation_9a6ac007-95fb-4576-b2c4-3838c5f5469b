import React, { useState } from 'react';
import { ActivityRecord, ActivityRecordType, ActivityRecordStatus } from '../types';
import { Mail, Send, Check, X, Eye } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface ActivityLogProps {
    records: ActivityRecord[];
}

const typeText: Record<ActivityRecordType, string> = {
    invitation_sent: '发出的邀请',
    invitation_received: '收到的邀请',
    application_sent: '发出的申请',
    application_received: '收到的申请',
};

const statusText: Record<ActivityRecordStatus, string> = {
    pending: '待处理',
    accepted: '已接受',
    rejected: '已拒绝',
    viewed: '已查看',
};

const statusIcon: Record<ActivityRecordStatus, React.FC<{ className?: string }>> = {
    pending: Mail,
    accepted: Check,
    rejected: X,
    viewed: Eye,
};

const statusColor: Record<ActivityRecordStatus, string> = {
    pending: 'text-yellow-600 bg-yellow-100',
    accepted: 'text-green-600 bg-green-100',
    rejected: 'text-red-600 bg-red-100',
    viewed: 'text-gray-600 bg-gray-100',
};

export const ActivityLog: React.FC<ActivityLogProps> = ({ records }) => {
    const [filter, setFilter] = useState<ActivityRecordType | 'all'>('all');

    const filteredRecords = records.filter(record => filter === 'all' || record.type === filter);

    if (records.length === 0) {
        return (
            <div className="bg-white rounded-2xl shadow-sm p-6 mb-6 text-center">
                <h2 className="text-xl font-semibold text-gray-900 mb-3">活动记录</h2>
                <p className="text-gray-500">暂无活动记录。</p>
            </div>
        );
    }

    return (
        <div className="bg-white rounded-2xl shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">活动记录</h2>

            {/* Filters */}
            <div className="flex space-x-2 mb-4 border-b border-gray-200 pb-2 overflow-x-auto">
                {(['all', 'invitation_sent', 'invitation_received', 'application_sent', 'application_received'] as const).map(f => (
                    <button
                        key={f}
                        onClick={() => setFilter(f)}
                        className={`px-3 py-1 rounded-md text-sm font-medium transition-colors flex-shrink-0 ${filter === f
                            ? 'bg-blue-600 text-white'
                            : 'text-gray-600 hover:bg-gray-100'
                            }`}
                    >
                        {f === 'all' ? '全部' : typeText[f]}
                    </button>
                ))}
            </div>

            {/* Record List */}
            <div className="space-y-3 max-h-80 overflow-y-auto pr-2">
                {filteredRecords.length > 0 ? (
                    filteredRecords.map(record => {
                        const Icon = statusIcon[record.status];
                        const color = statusColor[record.status];
                        let description = '';
                        switch (record.type) {
                            case 'invitation_sent': description = `邀请 ${record.targetUser || '用户'} 加入项目`; break;
                            case 'invitation_received': description = `${record.sourceUser || '用户'} 邀请你加入项目`; break;
                            case 'application_sent': description = `申请加入项目`; break;
                            case 'application_received': description = `${record.sourceUser || '用户'} 申请加入项目`; break;
                        }

                        return (
                            <div key={record.id} className="flex items-center justify-between p-3 rounded-lg bg-gray-50/80">
                                <div className="flex items-center space-x-3 min-w-0">
                                    <div className={`p-1.5 rounded-full ${color}`}>
                                        <Icon className="w-4 h-4" />
                                    </div>
                                    <div className="min-w-0">
                                        <p className="text-sm text-gray-800 truncate">
                                            {description} <span className="font-semibold">{record.projectName}</span>
                                        </p>
                                        <p className="text-xs text-gray-500">
                                            {formatDistanceToNow(new Date(record.timestamp), { addSuffix: true, locale: zhCN })}
                                        </p>
                                    </div>
                                </div>
                                <span className={`text-xs font-medium px-2 py-0.5 rounded-full ${color}`}>
                                    {statusText[record.status]}
                                </span>
                                {/* Placeholder for actions like Accept/Reject */}
                            </div>
                        );
                    })
                ) : (
                    <p className="text-center text-gray-500 py-4">此分类下暂无记录。</p>
                )}
            </div>
        </div>
    );
}; 