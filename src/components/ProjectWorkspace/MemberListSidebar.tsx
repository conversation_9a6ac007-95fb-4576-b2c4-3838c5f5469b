import React from 'react';
import { ProjectDetailData } from '../../types';
import { Users } from 'lucide-react';

interface MemberListSidebarProps {
    members: ProjectDetailData['members'];
}

export const MemberListSidebar: React.FC<MemberListSidebarProps> = ({ members }) => {
    return (
        <div className="w-full md:w-64 p-4 bg-white rounded-lg shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <Users className="w-5 h-5 mr-2 text-gray-500" />
                团队成员 ({members.length})
            </h3>
            <ul className="space-y-3">
                {members.map((member) => (
                    <li key={member.id} className="flex items-center space-x-3">
                        <img
                            src={member.avatar}
                            alt={member.name}
                            className="w-8 h-8 rounded-full object-cover border border-gray-200"
                        />
                        <span className="text-sm text-gray-700 font-medium">{member.name}</span>
                    </li>
                ))}
            </ul>
            {/* 可以添加邀请成员按钮等 */}
        </div>
    );
}; 