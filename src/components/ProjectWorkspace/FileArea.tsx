import React, { useState } from 'react';
import { UploadedFile, FileFolder, UserSummary } from '../../types';
import { getFileTypeIcon } from '../../utils/fileIcons';
import { Folder, MessageSquare, Upload, Trash2, Download, History } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { mockFolders, getMockFoldersForProject } from '../../data/mockProjects';

interface FileAreaProps {
    files: UploadedFile[];
    projectId: string;
}

export const FileArea: React.FC<FileAreaProps> = ({ files, projectId }) => {
    const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
    const [expandedCommentFileId, setExpandedCommentFileId] = useState<string | null>(null);
    const [expandedHistoryFileId, setExpandedHistoryFileId] = useState<string | null>(null);

    // Filter folders for the current project
    const projectFolders = getMockFoldersForProject(projectId);

    // Filter files based on selected folder
    const displayedFiles = files.filter(file => {
        if (selectedFolderId === null) return true; // Show all if 'All Files' selected
        return file.folderId === selectedFolderId;
    });

    const handleCommentToggle = (fileId: string) => {
        setExpandedCommentFileId(prevId => (prevId === fileId ? null : fileId));
        setExpandedHistoryFileId(null);
    };

    const handleHistoryToggle = (fileId: string) => {
        setExpandedHistoryFileId(prevId => (prevId === fileId ? null : fileId));
        setExpandedCommentFileId(null);
    };

    const handleUploadClick = () => {
        // Trigger file input or show modal - Mock for now
        alert('模拟文件上传');
    };

    const handleDeleteClick = (fileId: string, fileName: string) => {
        // Confirm deletion - Mock for now
        if (window.confirm(`确定要删除文件 "${fileName}" 吗？(模拟操作)`)) {
            console.log(`模拟删除文件: ${fileId}`);
            // Call onDelete prop if implemented
        }
    };

    const handleDownloadClick = (fileUrl: string, fileName: string) => {
        // Simulate download - In a real app, this might be a direct link or API call
        console.log(`模拟下载文件: ${fileName} from ${fileUrl}`);
        alert(`模拟开始下载: ${fileName}`);
    };

    return (
        <div className="flex flex-col md:flex-row gap-6">
            {/* Folder Sidebar */}
            <div className="w-full md:w-48 flex-shrink-0 bg-gray-50 p-3 rounded-lg border border-gray-100">
                <h4 className="text-sm font-semibold text-gray-700 mb-3 px-1">文件夹</h4>
                <nav className="space-y-1">
                    {/* All Files Button */}
                    <button
                        onClick={() => setSelectedFolderId(null)}
                        className={`w-full flex items-center px-3 py-1.5 text-sm rounded-md transition-colors 
                            ${selectedFolderId === null
                                ? 'bg-blue-100 text-blue-700 font-medium'
                                : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                            }`}
                    >
                        <Folder className="w-4 h-4 mr-2 flex-shrink-0" />
                        所有文件
                    </button>
                    {/* Project Folders */}
                    {projectFolders.map(folder => (
                        <button
                            key={folder.id}
                            onClick={() => setSelectedFolderId(folder.id)}
                            className={`w-full flex items-center px-3 py-1.5 text-sm rounded-md transition-colors 
                                ${selectedFolderId === folder.id
                                    ? 'bg-blue-100 text-blue-700 font-medium'
                                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                                }`}
                        >
                            <Folder className="w-4 h-4 mr-2 flex-shrink-0" />
                            <span className="truncate">{folder.name}</span>
                        </button>
                    ))}
                    {/* Add Folder Button (placeholder) */}
                    <button className="w-full flex items-center px-3 py-1.5 text-sm rounded-md text-gray-500 hover:bg-gray-100 hover:text-gray-700 mt-4 border-t pt-2 border-dashed">
                        <Upload className="w-4 h-4 mr-2" />
                        创建文件夹 (占位)
                    </button>
                </nav>
            </div>

            {/* File List Area */}
            <div className="flex-grow">
                {/* Naming Convention Hint */}
                <p className="text-xs text-gray-500 mb-3 bg-yellow-50 p-2 rounded border border-yellow-200">
                    提示：建议文件命名格式为 <strong>歌曲名_音轨名_v版本号.类型</strong> (例如: 夏日清凉_主唱_v2.wav)
                </p>

                {/* Upload Button (placeholder) */}
                <div className="mb-4 text-right">
                    <button className="inline-flex items-center px-3 py-1.5 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors">
                        <Upload className="w-4 h-4 mr-1.5" />
                        上传文件 (占位)
                    </button>
                </div>

                {/* File List */}
                {displayedFiles.length === 0 ? (
                    <div className="text-center py-10 text-gray-500">
                        此文件夹中暂无文件。
                    </div>
                ) : (
                    <ul className="space-y-2">
                        {displayedFiles.map(file => {
                            const FileIcon = getFileTypeIcon(file.type);
                            // Validate date before formatting
                            const uploadedDate = new Date(file.uploadedAt);
                            const uploadedTimeAgo = !isNaN(uploadedDate.getTime())
                                ? formatDistanceToNow(uploadedDate, { addSuffix: true, locale: zhCN })
                                : '无效日期'; // Fallback for invalid date
                            const hasComments = file.comments && file.comments.length > 0;
                            const hasHistory = file.previousVersions && file.previousVersions.length > 0;
                            const isCommentExpanded = expandedCommentFileId === file.id;
                            const isHistoryExpanded = expandedHistoryFileId === file.id;

                            return (
                                <li key={file.id} className="bg-white p-3 rounded-lg border border-gray-100 transition-shadow hover:shadow-sm">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center min-w-0 mr-4">
                                            <FileIcon className="w-5 h-5 text-gray-500 mr-3 flex-shrink-0" />
                                            <div className="min-w-0">
                                                <a href={file.fileUrl} target="_blank" rel="noopener noreferrer" className="text-sm font-medium text-gray-800 hover:text-blue-600 truncate block" title={file.name}>
                                                    {file.name}
                                                </a>
                                                <p className="text-xs text-gray-500 mt-0.5">
                                                    由 {file.uploader.name} 上传于 {uploadedTimeAgo}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-1 flex-shrink-0 ml-2">
                                            {/* History Button (Conditional) */}
                                            {hasHistory && (
                                                <button
                                                    onClick={() => handleHistoryToggle(file.id)}
                                                    className={`flex items-center text-xs px-2 py-1 rounded transition-colors text-gray-500 hover:bg-gray-100 hover:text-purple-600 ${isHistoryExpanded ? 'bg-purple-50 text-purple-600' : ''}`}
                                                    title="查看版本历史"
                                                >
                                                    <History className="w-3.5 h-3.5" />
                                                </button>
                                            )}
                                            {/* Comment Button */}
                                            <button
                                                onClick={() => handleCommentToggle(file.id)}
                                                className={`flex items-center text-xs px-2 py-1 rounded transition-colors ${hasComments ? 'text-blue-600 hover:bg-blue-50' : 'text-gray-500 hover:bg-gray-100'} ${isCommentExpanded ? 'bg-blue-50' : ''}`}
                                                title={hasComments ? `查看评论 (${file.comments?.length})` : '添加评论'}
                                            >
                                                <MessageSquare className="w-3.5 h-3.5 mr-1" />
                                                {hasComments ? file.comments?.length : '评论'}
                                            </button>
                                            {/* Download Button */}
                                            <a
                                                href={file.fileUrl}
                                                download={file.name}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="flex items-center text-xs px-2 py-1 rounded text-gray-500 hover:bg-gray-100 hover:text-blue-600 transition-colors"
                                                title={`下载 ${file.name} (当前版本)`}
                                            >
                                                <Download className="w-3.5 h-3.5" />
                                            </a>
                                        </div>
                                    </div>
                                    {/* Comment Section (Conditional) */}
                                    {isCommentExpanded && (
                                        <div className="mt-3 pt-3 border-t border-gray-100 pl-8 space-y-3">
                                            {(!file.comments || file.comments.length === 0) ? (
                                                <p className="text-xs text-gray-400 italic">暂无评论。</p>
                                            ) : (
                                                file.comments.map(comment => {
                                                    // Validate comment date before formatting
                                                    const commentDate = new Date(comment.timestamp);
                                                    const commentTimeAgo = !isNaN(commentDate.getTime())
                                                        ? formatDistanceToNow(commentDate, { addSuffix: true, locale: zhCN })
                                                        : '无效日期'; // Fallback
                                                    return (
                                                        <div key={comment.id} className="flex items-start space-x-2">
                                                            <img src={comment.author.avatar} alt={comment.author.name} className="w-5 h-5 rounded-full mt-0.5 flex-shrink-0" />
                                                            <div>
                                                                <p className="text-xs font-medium text-gray-700">{comment.author.name}</p>
                                                                <p className="text-xs text-gray-600 bg-gray-50 p-1.5 rounded mt-0.5">{comment.text}</p>
                                                                <p className="text-[10px] text-gray-400 mt-0.5">{commentTimeAgo}</p>
                                                            </div>
                                                        </div>
                                                    );
                                                })
                                            )}
                                            {/* Disabled Comment Input */}
                                            <div className="mt-2 pt-2 border-t border-dashed">
                                                <textarea
                                                    placeholder="添加评论... (功能开发中)"
                                                    rows={2}
                                                    className="w-full text-xs p-1.5 border rounded bg-gray-100 cursor-not-allowed"
                                                    disabled
                                                />
                                                <button className="text-xs px-2 py-1 bg-gray-300 text-gray-500 rounded cursor-not-allowed mt-1" disabled>提交</button>
                                            </div>
                                        </div>
                                    )}
                                    {/* History Section (Conditional) */}
                                    {isHistoryExpanded && hasHistory && (
                                        <div className="mt-3 pt-3 border-t border-gray-100 pl-8 space-y-2">
                                            <h5 className="text-xs font-semibold text-gray-600 mb-1">版本历史:</h5>
                                            {file.previousVersions?.map((version, index) => {
                                                const versionTimeAgo = !isNaN(new Date(version.uploadedAt).getTime())
                                                    ? formatDistanceToNow(new Date(version.uploadedAt), { addSuffix: true, locale: zhCN })
                                                    : '无效日期';
                                                return (
                                                    <div key={index} className="flex justify-between items-center text-xs border-b border-dashed border-gray-100 pb-1 last:border-b-0 last:pb-0">
                                                        <span>
                                                            版本上传于 {versionTimeAgo} 由 {version.uploader.name}
                                                        </span>
                                                        <a
                                                            href={version.fileUrl}
                                                            download={`${file.name}_v${file.previousVersions!.length - index - 1}`}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            className="text-blue-600 hover:underline ml-2"
                                                            title={`下载此版本`}
                                                        >
                                                            下载
                                                        </a>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    )}
                                </li>
                            );
                        })}
                    </ul>
                )}
            </div>
        </div>
    );
}; 