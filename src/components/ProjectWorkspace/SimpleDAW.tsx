import React from 'react';
import { UploadedFile } from '../../types';
import { Music, AlertTriangle } from 'lucide-react';

interface SimpleDAWProps {
    audioFiles: UploadedFile[];
}

export const SimpleDAW: React.FC<SimpleDAWProps> = ({ audioFiles }) => {

    // TODO: Implement simple DAW functionality using Tone.js or similar

    // Filter only audio files
    const availableSamples = audioFiles.filter(f => f.type === 'audio');

    return (
        <div className="p-4 bg-white rounded-lg shadow-sm border border-gray-100 min-h-[300px] flex flex-col items-center justify-center">
            <Music className="w-16 h-16 text-gray-300 mb-4" />
            <h3 className="text-lg font-semibold text-gray-700 mb-2">简易 DAW (开发中)</h3>
            <p className="text-sm text-gray-500 mb-4">这里将实现基础的音序器或多轨播放功能。</p>

            {availableSamples.length > 0 && (
                <div className="w-full max-w-md mt-4 p-3 bg-gray-50 rounded border border-gray-200">
                    <p className="text-xs text-gray-600 font-medium mb-2">可用音频样本:</p>
                    <ul className="text-xs text-gray-500 list-disc list-inside space-y-1">
                        {availableSamples.map(sample => (
                            <li key={sample.id} className="truncate" title={sample.name}>{sample.name}</li>
                        ))}
                    </ul>
                </div>
            )}

            <div className="mt-6 flex items-center text-orange-500 bg-orange-50 border border-orange-200 px-4 py-2 rounded-md">
                <AlertTriangle className="w-5 h-5 mr-2" />
                <span className="text-sm">注意: DAW 功能正在开发中，当前为占位符。</span>
            </div>
        </div>
    );
}; 