import React from 'react';
import { ProjectDetailData } from '../../types';
import { X, Phone, Mic, Video } from 'lucide-react';

interface VoiceCallModalProps {
    isOpen: boolean;
    onClose: () => void;
    members: ProjectDetailData['members'];
}

export const VoiceCallModal: React.FC<VoiceCallModalProps> = ({ isOpen, onClose, members }) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
            <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md relative animate-fade-in-scale">
                {/* Close Button */}
                <button
                    onClick={onClose}
                    className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 transition-colors"
                >
                    <X size={20} />
                </button>

                <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">模拟语音通话</h3>

                {/* Connecting/Status Indicator */}
                <div className="text-center text-sm text-green-600 mb-4">
                    已连接 (模拟状态)
                </div>

                {/* Member List */}
                <div className="mb-6 max-h-40 overflow-y-auto pr-2">
                    <p className="text-sm text-gray-600 mb-2">参与者:</p>
                    <ul className="space-y-2">
                        {members.map(member => (
                            <li key={member.id} className="flex items-center space-x-2">
                                <img src={member.avatar} alt={member.name} className="w-6 h-6 rounded-full" />
                                <span className="text-sm text-gray-700">{member.name}</span>
                                {/* Add simulated mic/video status icons maybe */}
                            </li>
                        ))}
                    </ul>
                </div>

                {/* Call Controls (Mock) */}
                <div className="flex justify-center space-x-4">
                    <button className="p-3 bg-gray-200 rounded-full text-gray-700 hover:bg-gray-300 transition-colors">
                        <Mic size={20} />
                    </button>
                    <button className="p-3 bg-gray-200 rounded-full text-gray-700 hover:bg-gray-300 transition-colors">
                        <Video size={20} />
                    </button>
                    <button
                        onClick={onClose} // Close modal simulates hanging up
                        className="p-3 bg-red-500 rounded-full text-white hover:bg-red-600 transition-colors"
                    >
                        <Phone size={20} />
                    </button>
                </div>
            </div>
        </div>
    );
};

// Add simple animation if needed (add this to your global CSS or tailwind.config.js)
/*
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
.animate-fade-in-scale {
  animation: fadeInScale 0.2s ease-out forwards;
}
*/ 