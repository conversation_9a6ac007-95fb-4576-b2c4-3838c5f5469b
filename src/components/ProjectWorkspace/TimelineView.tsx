import React from 'react';
import { VerticalTimeline, VerticalTimelineElement } from 'react-vertical-timeline-component';
import 'react-vertical-timeline-component/style.min.css';
import { TimelineEvent } from '../../types';
import { CheckCircle, Clock, Loader, Edit3, Music, Mic, GitMerge } from 'lucide-react'; // Import icons

interface TimelineViewProps {
    timeline: TimelineEvent[];
}

// Helper to get icon based on status or title (customize as needed)
const getTimelineIcon = (event: TimelineEvent) => {
    if (event.icon) return event.icon; // Use predefined icon if available (removed from mock data)

    // Fallback based on status or title keywords
    if (event.status === 'completed') return <CheckCircle className="w-4 h-4" />;
    if (event.status === 'ongoing') return <Loader className="w-4 h-4 animate-spin" />;
    if (event.status === 'planned') return <Clock className="w-4 h-4" />;

    // Fallback based on title keywords (examples)
    if (event.title.includes('创作') || event.title.includes('编写')) return <Edit3 className="w-4 h-4" />;
    if (event.title.includes('录制') || event.title.includes('歌手') || event.title.includes('招募')) return <Mic className="w-4 h-4" />;
    if (event.title.includes('混音') || event.title.includes('合并')) return <GitMerge className="w-4 h-4" />;

    return <Music className="w-4 h-4" />; // Default icon
};

// Helper to get color based on status
const getTimelineColor = (status: TimelineEvent['status']) => {
    switch (status) {
        case 'completed': return '#10B981'; // Green-500
        case 'ongoing': return '#3B82F6'; // Blue-500
        case 'planned': return '#6B7280'; // Gray-500
        default: return '#6B7280';
    }
};

export const TimelineView: React.FC<TimelineViewProps> = ({ timeline }) => {
    if (!timeline || timeline.length === 0) {
        return <div className="p-4 text-gray-500">暂无时间线信息。</div>;
    }

    return (
        <div className="py-4">
            <VerticalTimeline lineColor="#E5E7EB" layout="1-column-left">
                {timeline.map((event) => (
                    <VerticalTimelineElement
                        key={event.id}
                        className="vertical-timeline-element--work"
                        contentStyle={{ background: '#ffffff', color: '#374151', boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)', borderTop: `3px solid ${getTimelineColor(event.status)}` }}
                        contentArrowStyle={{ borderRight: '7px solid #ffffff' }}
                        date={event.date}
                        iconStyle={{ background: getTimelineColor(event.status), color: '#fff', width: '40px', height: '40px', marginLeft: '-20px' }} // Adjust marginLeft for 1-column
                        icon={getTimelineIcon(event)}
                    >
                        <h3 className="vertical-timeline-element-title font-semibold text-gray-800">{event.title}</h3>
                        {event.description && <p className="text-sm text-gray-600 mt-1">{event.description}</p>}
                        <span className={`mt-2 inline-block px-2 py-0.5 text-xs rounded-full ${event.status === 'completed' ? 'bg-green-100 text-green-700' : event.status === 'ongoing' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'
                            }`}>
                            {event.status === 'completed' ? '已完成' : event.status === 'ongoing' ? '进行中' : '计划中'}
                        </span>
                    </VerticalTimelineElement>
                ))}
            </VerticalTimeline>
        </div>
    );
}; 