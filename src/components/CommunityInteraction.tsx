import React from 'react';
import { MessageSquare, ThumbsUp } from 'lucide-react';
import { CommunityPost } from '../types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

// 模拟社群互动数据
const mockPosts: CommunityPost[] = [
    {
        id: 'p1',
        title: '求助：如何让我的混音更有空间感？',
        author: {
            name: '混音小白',
            avatar: 'https://api.dicebear.com/7.x/fun-emoji/svg?seed=Felix'
        },
        timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15分钟前
        snippet: '我尝试了各种混响和延迟，但总感觉声音挤在一起，不够开阔...',
        commentCount: 8,
        likeCount: 15,
        link: '/community/post/p1'
    },
    {
        id: 'p2',
        title: '分享一个我刚完成的 Lo-Fi HipHop Beat',
        author: {
            name: '节奏玩家',
            avatar: 'https://api.dicebear.com/7.x/shapes/svg?seed=Jasper'
        },
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2小时前
        snippet: '欢迎大家听听给点意见！使用了 SP404 和 Ableton Live 制作。',
        commentCount: 12,
        likeCount: 35,
        link: '/community/post/p2'
    },
    {
        id: 'p3',
        title: '讨论：AI 在音乐创作中的应用前景？',
        author: {
            name: '科技探索者',
            avatar: 'https://api.dicebear.com/7.x/identicon/svg?seed=Bandit'
        },
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1天前
        snippet: '最近试用了几个 AI 作曲工具，感觉很神奇，但也有些担忧...',
        commentCount: 25,
        likeCount: 48,
        link: '/community/post/p3'
    },
];

export const CommunityInteraction: React.FC = () => {
    return (
        <div className="bg-white rounded-2xl shadow-sm p-6 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-5 flex items-center">
                <MessageSquare className="w-6 h-6 mr-2 text-teal-500" />
                社群互动
            </h2>
            <div className="space-y-5">
                {mockPosts.map((post) => (
                    <div key={post.id} className="border-b border-gray-100 pb-5 last:border-b-0 last:pb-0">
                        <div className="flex items-start space-x-3">
                            <img
                                src={post.author.avatar}
                                alt={post.author.name}
                                className="w-10 h-10 rounded-full bg-gray-100 mt-1"
                            />
                            <div className="flex-1">
                                <a href={post.link} className="hover:text-blue-600 transition-colors">
                                    <h3 className="font-semibold text-gray-800 mb-1 leading-snug">{post.title}</h3>
                                </a>
                                <p className="text-sm text-gray-600 mb-2 line-clamp-2">{post.snippet}</p>
                                <div className="flex items-center justify-between text-xs text-gray-500">
                                    <span>{post.author.name} • {formatDistanceToNow(new Date(post.timestamp), { addSuffix: true, locale: zhCN })}</span>
                                    <div className="flex items-center space-x-3">
                                        <span className="flex items-center">
                                            <ThumbsUp className="w-3.5 h-3.5 mr-1" /> {post.likeCount}
                                        </span>
                                        <span className="flex items-center">
                                            <MessageSquare className="w-3.5 h-3.5 mr-1" /> {post.commentCount}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
            <button className="mt-5 text-sm text-blue-600 hover:text-blue-800 w-full text-center font-medium">
                查看更多社群动态
            </button>
        </div>
    );
}; 