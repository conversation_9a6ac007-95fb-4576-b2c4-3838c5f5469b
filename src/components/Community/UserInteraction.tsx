import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { UserPlus, MessageSquare, Bell } from 'lucide-react';
import { AuthState } from '../../types';

interface UserInteractionProps {
    auth: AuthState;
}

// 模拟推荐用户数据
const recommendedUsers = [
    {
        id: 'u1',
        name: '音乐制作人',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=MusicProducer',
        bio: '专注于电子音乐制作'
    },
    {
        id: 'u2',
        name: '混音师',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=MixingEngineer',
        bio: '专业混音工程师'
    },
    {
        id: 'u3',
        name: '作曲人',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Composer',
        bio: '创作各种风格的音乐'
    }
];

export const UserInteraction: React.FC<UserInteractionProps> = ({ auth }) => {
    const [following, setFollowing] = useState<Set<string>>(new Set());
    const navigate = useNavigate();

    const handleFollow = (userId: string) => {
        setFollowing(prev => {
            const newSet = new Set(prev);
            if (newSet.has(userId)) {
                newSet.delete(userId);
            } else {
                newSet.add(userId);
            }
            return newSet;
        });
    };

    const handleMessage = (userId: string) => {
        console.log('Attempting to message user:', userId);
        navigate(`/messages/new/${userId}`);
    };

    return (
        <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">推荐关注</h3>
            <div className="space-y-4">
                {recommendedUsers.map(user => (
                    <div key={user.id} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <img
                                src={user.avatar}
                                alt={user.name}
                                className="w-10 h-10 rounded-full bg-gray-100"
                            />
                            <div>
                                <p className="font-medium text-gray-900">{user.name}</p>
                                <p className="text-xs text-gray-500">{user.bio}</p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-2">
                            <button
                                onClick={() => handleFollow(user.id)}
                                className={`p-2 rounded-full transition-colors ${following.has(user.id)
                                    ? 'bg-blue-100 text-blue-600'
                                    : 'text-gray-500 hover:bg-gray-100'
                                    }`}
                                title={following.has(user.id) ? '取消关注' : '关注'}
                            >
                                <UserPlus className="w-4 h-4" />
                            </button>
                            <button
                                onClick={() => handleMessage(user.id)}
                                className="p-2 text-gray-500 hover:bg-gray-100 rounded-full transition-colors"
                                title="发送消息"
                            >
                                <MessageSquare className="w-4 h-4" />
                            </button>
                        </div>
                    </div>
                ))}
            </div>
            <div className="mt-6 pt-6 border-t border-gray-100">
                <button className="w-full flex items-center justify-center space-x-2 text-blue-600 hover:text-blue-800 transition-colors">
                    <Bell className="w-4 h-4" />
                    <span>查看全部通知</span>
                </button>
            </div>
        </div>
    );
}; 