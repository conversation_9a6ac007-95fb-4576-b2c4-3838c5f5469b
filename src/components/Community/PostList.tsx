import React, { useState, useEffect } from 'react';
import { MessageSquare, ThumbsUp, Share2, MoreHorizontal, Bookmark, Tag, UserPlus, Check } from 'lucide-react';
import { CommunityPost, AuthState, PostCategory } from '../../types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { Link } from 'react-router-dom';

interface PostListProps {
    auth: AuthState;
    sortBy?: 'latest' | 'hot' | 'following';
    selectedCategory?: PostCategory | 'all';
}

// 模拟帖子数据
const mockPosts: CommunityPost[] = [
    {
        id: 'p1',
        title: '求助：如何让我的混音更有空间感？',
        author: {
            id: 'u1',
            name: '混音小白',
            avatar: 'https://api.dicebear.com/7.x/fun-emoji/svg?seed=Felix'
        },
        timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
        snippet: '我尝试了各种混响和延迟，但总感觉声音挤在一起，不够开阔...',
        commentCount: 8,
        likeCount: 15,
        link: '/community/post/p1',
        tags: ['#混音', '#空间感', '#求助'],
        category: '提问'
    },
    {
        id: 'p2',
        title: '分享一个我刚完成的 Lo-Fi HipHop Beat',
        author: {
            id: 'u2',
            name: '节奏玩家',
            avatar: 'https://api.dicebear.com/7.x/shapes/svg?seed=Jasper'
        },
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        snippet: '欢迎大家听听给点意见！使用了 SP404 和 Ableton Live 制作。',
        commentCount: 12,
        likeCount: 35,
        link: '/community/post/p2',
        tags: ['#分享', '#LoFi', '#HipHop', '#Beatmaking'],
        category: '作品展示'
    },
    {
        id: 'p3',
        title: '讨论：AI 在音乐创作中的应用前景？',
        author: {
            id: 'u3',
            name: '科技探索者',
            avatar: 'https://api.dicebear.com/7.x/identicon/svg?seed=Bandit'
        },
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
        snippet: '最近试用了几个 AI 作曲工具，感觉很神奇，但也有些担忧...',
        commentCount: 25,
        likeCount: 48,
        link: '/community/post/p3',
        tags: ['#讨论', '#AI', '#音乐创作', '#未来'],
        category: '讨论'
    },
    {
        id: 'p4',
        title: '新手编曲入门心得分享',
        author: {
            id: 'u4',
            name: '编曲萌新',
            avatar: 'https://api.dicebear.com/7.x/pixel-art/svg?seed=Lucy'
        },
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(),
        snippet: '刚开始学编曲一个月，分享一些踩过的坑和有用的资源...',
        commentCount: 5,
        likeCount: 22,
        link: '/community/post/p4',
        tags: ['#分享', '#编曲', '#新手', '#教程', '#音乐创作'],
        category: '分享'
    }
];

export const PostList: React.FC<PostListProps> = ({ auth, sortBy = 'latest', selectedCategory = 'all' }) => {
    const [displayedPosts, setDisplayedPosts] = useState<CommunityPost[]>([]);
    const [likedPosts, setLikedPosts] = useState<Set<string>>(new Set());
    const [collectedPosts, setCollectedPosts] = useState<Set<string>>(new Set());
    const [localFollowingState, setLocalFollowingState] = useState<Set<string>>(new Set());

    useEffect(() => {
        if (auth.isAuthenticated && auth.user) {
            setLocalFollowingState(new Set(auth.user.following.map(u => u.id)));
        }
    }, [auth.user, auth.isAuthenticated]);

    useEffect(() => {
        let processedPosts = [...mockPosts];

        if (selectedCategory !== 'all') {
            processedPosts = processedPosts.filter(post => post.category === selectedCategory);
        }

        switch (sortBy) {
            case 'hot':
                processedPosts.sort((a, b) => {
                    const scoreA = (a.likeCount || 0) + (a.commentCount || 0) * 2;
                    const scoreB = (b.likeCount || 0) + (b.commentCount || 0) * 2;
                    return scoreB - scoreA;
                });
                break;
            case 'following':
                if (auth.isAuthenticated && auth.user?.following) {
                    const followingIds = new Set(auth.user.following.map(u => u.id));
                    processedPosts = processedPosts.filter(post =>
                        followingIds.has(post.author.id)
                    );
                    processedPosts.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
                } else {
                    processedPosts = [];
                }
                break;
            case 'latest':
            default:
                processedPosts.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
                break;
        }
        console.log(`[PostList] Filtering by Category: ${selectedCategory}, Sorting/Filtering by: ${sortBy}`, processedPosts);
        setDisplayedPosts(processedPosts);
    }, [sortBy, auth, selectedCategory]);

    const handleLike = (postId: string) => {
        if (!auth.isAuthenticated) {
            // 可以在这里添加登录提示
            return;
        }

        setLikedPosts(prev => {
            const newSet = new Set(prev);
            if (newSet.has(postId)) {
                newSet.delete(postId);
            } else {
                newSet.add(postId);
            }
            return newSet;
        });

        setDisplayedPosts(prev => prev.map(post => {
            if (post.id === postId) {
                return {
                    ...post,
                    likeCount: likedPosts.has(postId) ? post.likeCount - 1 : post.likeCount + 1
                };
            }
            return post;
        }));
    };

    const handleCollect = (postId: string) => {
        if (!auth.isAuthenticated) {
            // Add login prompt if needed
            return;
        }
        setCollectedPosts(prev => {
            const newSet = new Set(prev);
            if (newSet.has(postId)) {
                newSet.delete(postId);
            } else {
                newSet.add(postId);
            }
            return newSet;
        });
        console.log(`Toggled collection for post: ${postId}. New status: ${!collectedPosts.has(postId)}`);
        // Optionally update collect count if tracking it in posts state
    };

    const handleShare = (postId: string) => {
        // 实现分享功能
        console.log('Sharing post:', postId);
    };

    const handleFollowToggle = (targetUserId: string) => {
        if (!auth.isAuthenticated) return;

        const isCurrentlyFollowing = localFollowingState.has(targetUserId);
        console.log(`[PostList] Attempting to ${isCurrentlyFollowing ? 'unfollow' : 'follow'} user: ${targetUserId}`);

        setLocalFollowingState(prev => {
            const newSet = new Set(prev);
            if (isCurrentlyFollowing) {
                newSet.delete(targetUserId);
            } else {
                newSet.add(targetUserId);
            }
            return newSet;
        });
        // In a real app, call an API here to update the backend and central state
    };

    return (
        <div className="space-y-6">
            {displayedPosts.length > 0 ? (
                displayedPosts.map(post => {
                    const isAuthorSelf = auth.user?.id === post.author.id;
                    const isFollowingAuthor = localFollowingState.has(post.author.id);

                    return (
                        <div key={post.id} className="bg-white rounded-xl shadow-sm p-6">
                            <div className="flex items-start space-x-3">
                                <img
                                    src={post.author.avatar}
                                    alt={post.author.name}
                                    className="w-10 h-10 rounded-full bg-gray-100 mt-1"
                                />
                                <div className="flex-1">
                                    <div className="flex items-start justify-between">
                                        <Link to={`/community/post/${post.id}`} className="hover:text-blue-600 transition-colors">
                                            <h3 className="font-semibold text-gray-800 mb-1 leading-snug">{post.title}</h3>
                                        </Link>
                                        <button className="text-gray-400 hover:text-gray-600 ml-2">
                                            <MoreHorizontal className="w-5 h-5" />
                                        </button>
                                    </div>
                                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">{post.snippet}</p>

                                    {post.tags && post.tags.length > 0 && (
                                        <div className="flex flex-wrap gap-2 mb-3">
                                            {post.tags.map((tag, index) => (
                                                <Link
                                                    key={index}
                                                    to={`/community/tags/${encodeURIComponent(tag.substring(1))}`}
                                                    className="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs hover:bg-gray-200 transition-colors"
                                                >
                                                    {tag}
                                                </Link>
                                            ))}
                                        </div>
                                    )}

                                    <div className="flex items-center justify-between text-xs text-gray-500">
                                        <div className="flex items-center space-x-2">
                                            <span>{post.author.name}</span>
                                            {auth.isAuthenticated && !isAuthorSelf && (
                                                <button
                                                    onClick={() => handleFollowToggle(post.author.id)}
                                                    className={`px-2 py-0.5 rounded text-xs transition-colors ${isFollowingAuthor
                                                        ? 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                                                        : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                                                        }`}
                                                >
                                                    {isFollowingAuthor ? (
                                                        <Check className="w-3 h-3 inline-block mr-0.5" />
                                                    ) : (
                                                        <UserPlus className="w-3 h-3 inline-block mr-0.5" />
                                                    )}
                                                    {isFollowingAuthor ? '已关注' : '关注'}
                                                </button>
                                            )}
                                            <span>• {formatDistanceToNow(new Date(post.timestamp), { addSuffix: true, locale: zhCN })}</span>
                                        </div>
                                        <div className="flex items-center space-x-4">
                                            <button
                                                onClick={() => handleLike(post.id)}
                                                className={`flex items-center space-x-1 ${likedPosts.has(post.id) ? 'text-blue-600' : 'text-gray-500'
                                                    } hover:text-blue-600 transition-colors`}
                                            >
                                                <ThumbsUp className="w-4 h-4" />
                                                <span>{post.likeCount}</span>
                                            </button>
                                            <Link
                                                to={`/community/post/${post.id}`}
                                                className="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors"
                                            >
                                                <MessageSquare className="w-4 h-4" />
                                                <span>{post.commentCount}</span>
                                            </Link>
                                            <button
                                                onClick={() => handleCollect(post.id)}
                                                className={`flex items-center space-x-1 ${collectedPosts.has(post.id) ? 'text-yellow-600' : 'text-gray-500'
                                                    } hover:text-yellow-600 transition-colors`}
                                            >
                                                <Bookmark className={`w-4 h-4 ${collectedPosts.has(post.id) ? 'fill-current' : ''}`} />
                                            </button>
                                            <button
                                                onClick={() => handleShare(post.id)}
                                                className="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors"
                                            >
                                                <Share2 className="w-4 h-4" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    );
                })
            ) : (
                <div className="text-center py-10 text-gray-500">
                    {sortBy === 'following' && !auth.isAuthenticated ? '请先登录查看关注动态' :
                        sortBy === 'following' ? '您还没有关注任何人，或关注的人暂无动态' :
                            selectedCategory !== 'all' ? `暂无"${selectedCategory}"分类下的帖子` :
                                '暂无帖子'}
                </div>
            )}
        </div>
    );
}; 