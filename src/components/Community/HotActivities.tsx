import React from 'react';
import { HotActivity } from '../../types';

// 模拟热门活动数据
const mockActivities: HotActivity[] = [
    {
        id: 'act1',
        title: '【官方活动】夏季音乐创作挑战赛开启！',
        imageUrl: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=870&q=80', // 替换为实际图片URL
        link: '/community/activity/summer-challenge',
        tag: '官方',
    },
    {
        id: 'act2',
        title: '音乐人交流沙龙：探索编曲新思路',
        imageUrl: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=870&q=80', // 替换为实际图片URL
        link: '/community/activity/arranger-salon',
        tag: '线下',
    },
    {
        id: 'act3',
        title: '限时福利：免费获取专业混音插件！',
        imageUrl: 'https://images.unsplash.com/photo-1502086223501-7ea6ecd79368?w=870&q=80',
        link: '/community/activity/free-plugin',
        tag: '福利',
    },
];

export const HotActivities: React.FC = () => {
    return (
        <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">热门活动</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {mockActivities.map(activity => (
                    <a
                        key={activity.id}
                        href={activity.link} // 使用原生 href 或 <Link> 组件
                        className="group block bg-white rounded-lg shadow-sm overflow-hidden transition-transform transform hover:scale-105 duration-300 ease-in-out"
                        target="_blank" // 如果是外部链接，建议添加
                        rel="noopener noreferrer" // 安全性考虑
                    >
                        <div className="relative h-40 overflow-hidden">
                            <img
                                src={activity.imageUrl}
                                alt={activity.title}
                                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                            />
                            {activity.tag && (
                                <span className="absolute top-2 left-2 bg-red-500 text-white text-xs font-semibold px-2 py-1 rounded">
                                    {activity.tag}
                                </span>
                            )}
                        </div>
                        <div className="p-4">
                            <h3 className="text-base font-semibold text-gray-800 group-hover:text-blue-600 transition-colors truncate">
                                {activity.title}
                            </h3>
                            {/* 可以选择性地添加描述 */}
                            {/* {activity.description && <p className="text-sm text-gray-600 mt-1 truncate">{activity.description}</p>} */}
                        </div>
                    </a>
                ))}
            </div>
        </div>
    );
}; 