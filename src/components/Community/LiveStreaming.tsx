import React from 'react';
import { LiveStreamInfo } from '../../types';
import { ChevronRight } from 'lucide-react';

// 模拟直播数据 (根据用户图片)
const mockLiveStreams: LiveStreamInfo[] = [
    {
        id: 'live1',
        streamerName: '泰勒·艾莉森·斯威夫特',
        streamerAvatar: 'https://images.unsplash.com/photo-1548778052-311f4bc2b502?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        streamTitle: '泰勒·艾莉森·斯威夫特专场',
        streamLink: '/live/mr-mideng',
    },
    // 可以添加更多模拟数据
];

export const LiveStreaming: React.FC = () => {
    const liveCount = mockLiveStreams.length;

    return (
        <div className="mb-6 bg-white p-4 rounded-lg shadow-sm">
            <div className="flex justify-between items-center mb-4">
                <div className="flex items-baseline">
                    <h3 className="text-lg font-semibold text-gray-900">正在直播</h3>
                    <span className="text-sm text-gray-500 ml-1.5">{liveCount}</span>
                </div>
                <a href="#" className="flex items-center text-sm text-gray-500 hover:text-blue-600 transition-colors">
                    更多关注
                    <ChevronRight className="w-4 h-4 ml-0.5" />
                </a>
            </div>
            <div className="space-y-4">
                {mockLiveStreams.map(stream => (
                    <a key={stream.id} href={stream.streamLink} className="flex items-center space-x-3 group">
                        <div className="relative flex-shrink-0">
                            <img
                                src={stream.streamerAvatar}
                                alt={stream.streamerName}
                                className="w-12 h-12 rounded-full object-cover border border-gray-200"
                            />
                            {/* "直播中" 标签 - 尝试模仿图片样式 */}
                            <span className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 bg-pink-500 text-white text-[10px] px-1.5 py-0.5 rounded-full whitespace-nowrap shadow">
                                直播中
                            </span>
                        </div>
                        <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-800 truncate group-hover:text-blue-600 transition-colors">
                                {stream.streamerName}
                            </p>
                            <p className="text-xs text-gray-600 truncate">
                                {stream.streamTitle}
                            </p>
                        </div>
                    </a>
                ))}
            </div>
        </div>
    );
}; 