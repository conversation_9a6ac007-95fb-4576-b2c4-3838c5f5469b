import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { CommunityPost } from '../../types';
import { Tag } from 'lucide-react';

// --- Temporary Mock Data (Copied from TagFilteredPosts) ---
const allMockPosts: CommunityPost[] = [
    {
        id: 'p1',
        title: '求助：如何让我的混音更有空间感？',
        author: { id: 'u1', name: '混音小白', avatar: 'https://api.dicebear.com/7.x/fun-emoji/svg?seed=Felix' },
        timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
        snippet: '我尝试了各种混响和延迟，但总感觉声音挤在一起，不够开阔...',
        commentCount: 8,
        likeCount: 15,
        link: '/community/post/p1',
        tags: ['#混音', '#空间感', '#求助']
    },
    {
        id: 'p2',
        title: '分享一个我刚完成的 Lo-Fi HipHop Beat',
        author: { id: 'u2', name: '节奏玩家', avatar: 'https://api.dicebear.com/7.x/shapes/svg?seed=Jasper' },
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        snippet: '欢迎大家听听给点意见！使用了 SP404 和 Ableton Live 制作。',
        commentCount: 12,
        likeCount: 35,
        link: '/community/post/p2',
        tags: ['#分享', '#LoFi', '#HipHop', '#Beatmaking']
    },
    {
        id: 'p3',
        title: '讨论：AI 在音乐创作中的应用前景？',
        author: { id: 'u3', name: '科技探索者', avatar: 'https://api.dicebear.com/7.x/identicon/svg?seed=Bandit' },
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
        snippet: '最近试用了几个 AI 作曲工具，感觉很神奇，但也有些担忧...',
        commentCount: 25,
        likeCount: 48,
        link: '/community/post/p3',
        tags: ['#讨论', '#AI', '#音乐创作', '#未来']
    },
    {
        id: 'p4', // Adding another post for variety
        title: '新手编曲入门心得分享',
        author: { id: 'u4', name: '编曲萌新', avatar: 'https://api.dicebear.com/7.x/pixel-art/svg?seed=Lucy' },
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(),
        snippet: '刚开始学编曲一个月，分享一些踩过的坑和有用的资源...',
        commentCount: 5,
        likeCount: 22,
        link: '/community/post/p4',
        tags: ['#分享', '#编曲', '#新手', '#教程', '#音乐创作']
    }
];
// --- End Temporary Mock Data ---

const MAX_TRENDING_TAGS = 5;

export const TrendingTags: React.FC = () => {
    const [trendingTags, setTrendingTags] = useState<string[]>([]);

    useEffect(() => {
        // Calculate trending tags from mock data
        const tagCounts: { [key: string]: number } = {};

        allMockPosts.forEach(post => {
            post.tags?.forEach(tag => {
                const tagName = tag.startsWith('#') ? tag.substring(1) : tag;
                if (tagName) {
                    tagCounts[tagName] = (tagCounts[tagName] || 0) + 1;
                }
            });
        });

        const sortedTags = Object.entries(tagCounts)
            .sort(([, countA], [, countB]) => countB - countA) // Sort by count descending
            .map(([tagName]) => tagName); // Get only the tag names

        setTrendingTags(sortedTags.slice(0, MAX_TRENDING_TAGS));

    }, []); // Run only once on mount

    if (trendingTags.length === 0) {
        return null; // Don't render anything if no tags found
    }

    return (
        <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Tag className="w-5 h-5 mr-2 text-pink-500" />
                热门话题
            </h3>
            <div className="flex flex-wrap gap-2">
                {trendingTags.map((tag, index) => (
                    <Link
                        key={index}
                        to={`/community/tags/${encodeURIComponent(tag)}`}
                        className="px-2.5 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 hover:text-gray-900 transition-colors"
                    >
                        # {tag}
                    </Link>
                ))}
            </div>
        </div>
    );
}; 