import React, { useState } from 'react';
import { Image, Link2, Send, X, Eye, Users } from 'lucide-react';
import { AuthState, POST_CATEGORIES, PostCategory, PostVisibility } from '../../types';

interface CreatePostProps {
    auth: AuthState;
    onPostSuccess?: () => void;
}

export const CreatePost: React.FC<CreatePostProps> = ({ auth, onPostSuccess }) => {
    const [content, setContent] = useState('');
    const [tags, setTags] = useState<string[]>([]);
    const [tagInput, setTagInput] = useState('');
    const [category, setCategory] = useState<PostCategory | ''>('');
    const [visibility, setVisibility] = useState<PostVisibility>('public');
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setTagInput(e.target.value);
    };

    const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter' && tagInput.trim() !== '') {
            e.preventDefault();
            const newTag = tagInput.trim();
            const formattedTag = newTag.startsWith('#') ? newTag : `#${newTag}`;
            if (!tags.includes(formattedTag) && tags.length < 5) {
                setTags([...tags, formattedTag]);
            }
            setTagInput('');
        }
    };

    const handleRemoveTag = (tagToRemove: string) => {
        setTags(tags.filter(tag => tag !== tagToRemove));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!content.trim() || !category || isSubmitting) {
            if (!category) {
                alert('请选择一个帖子分类！');
            }
            return;
        }

        setIsSubmitting(true);
        try {
            const postData = {
                content: content.trim(),
                tags: tags,
                category: category,
                visibility: visibility,
            };
            console.log('Posting data:', postData);

            await new Promise(resolve => setTimeout(resolve, 500));

            onPostSuccess?.();

            setContent('');
            setTags([]);
            setTagInput('');
            setCategory('');
            setVisibility('public');
        } catch (error) {
            console.error('Failed to post:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">发布新帖子</h3>
            <form onSubmit={handleSubmit}>
                <div className="space-y-4">
                    <div>
                        <label htmlFor="post-category" className="block text-sm font-medium text-gray-700 mb-1">选择分类 <span className="text-red-500">*</span></label>
                        <select
                            id="post-category"
                            value={category}
                            onChange={(e) => setCategory(e.target.value as PostCategory)}
                            className="w-full border border-gray-200 rounded-lg p-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            required
                        >
                            <option value="" disabled>-- 请选择 --</option>
                            {POST_CATEGORIES.map(cat => (
                                <option key={cat} value={cat}>{cat}</option>
                            ))}
                        </select>
                    </div>
                    <textarea
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        placeholder="分享你的想法..."
                        rows={5}
                        className="w-full border border-gray-200 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    />
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">添加标签 (输入后回车确认)</label>
                        <div className="flex flex-wrap items-center gap-2 p-2 border border-gray-200 rounded-lg min-h-[40px]">
                            {tags.map((tag, index) => (
                                <span key={index} className="flex items-center px-2 py-0.5 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
                                    {tag}
                                    <button
                                        type="button"
                                        onClick={() => handleRemoveTag(tag)}
                                        className="ml-1.5 text-blue-500 hover:text-blue-700 focus:outline-none"
                                        aria-label={`Remove tag ${tag}`}
                                    >
                                        <X className="w-3 h-3" />
                                    </button>
                                </span>
                            ))}
                            <input
                                type="text"
                                value={tagInput}
                                onChange={handleTagInputChange}
                                onKeyDown={handleTagInputKeyDown}
                                placeholder={tags.length < 5 ? '最多添加 5 个标签...' : '已达标签上限'}
                                className="flex-grow p-1 outline-none text-sm bg-transparent"
                                disabled={tags.length >= 5}
                            />
                        </div>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">谁可以看?</label>
                        <div className="flex items-center space-x-4">
                            <label className="flex items-center space-x-2 cursor-pointer">
                                <input
                                    type="radio"
                                    name="visibility"
                                    value="public"
                                    checked={visibility === 'public'}
                                    onChange={() => setVisibility('public')}
                                    className="form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                />
                                <Eye className={`w-4 h-4 ${visibility === 'public' ? 'text-blue-600' : 'text-gray-500'}`} />
                                <span className={`text-sm ${visibility === 'public' ? 'font-medium text-gray-900' : 'text-gray-600'}`}>公开</span>
                            </label>
                            <label className="flex items-center space-x-2 cursor-pointer">
                                <input
                                    type="radio"
                                    name="visibility"
                                    value="followers"
                                    checked={visibility === 'followers'}
                                    onChange={() => setVisibility('followers')}
                                    className="form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                />
                                <Users className={`w-4 h-4 ${visibility === 'followers' ? 'text-blue-600' : 'text-gray-500'}`} />
                                <span className={`text-sm ${visibility === 'followers' ? 'font-medium text-gray-900' : 'text-gray-600'}`}>仅关注者</span>
                            </label>
                        </div>
                    </div>
                    <div className="flex items-center justify-between pt-2">
                        <div className="flex items-center space-x-2">
                            <button
                                type="button"
                                className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                                title="添加图片"
                            >
                                <Image className="w-5 h-5" />
                            </button>
                            <button
                                type="button"
                                className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                                title="添加链接"
                            >
                                <Link2 className="w-5 h-5" />
                            </button>
                        </div>
                        <button
                            type="submit"
                            disabled={!content.trim() || !category || isSubmitting}
                            className="px-6 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center space-x-2 font-medium"
                        >
                            <Send className="w-4 h-4" />
                            <span>发布帖子</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    );
}; 