import React, { useState, useEffect } from 'react';
import { DiscussionComment, AuthState } from '../../types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { ThumbsUp, MessageSquare, MoreHorizontal, Send, UserPlus, Check } from 'lucide-react';

// Define type for the recursive structure
export interface CommentWithReplies extends DiscussionComment {
    replies?: CommentWithReplies[];
}

interface CommentItemProps {
    comment: CommentWithReplies; // Use CommentWithReplies type
    auth: AuthState;
    allComments: DiscussionComment[]; // Pass all comments for finding replies
    onReplySubmit: (content: string, parentId: string) => void; // Function to handle submitting a reply
    replyingToId: string | null; // ID of the comment currently being replied to
    setReplyingToId: (id: string | null) => void; // Function to set the replyingToId
    level?: number; // Recursion level for indentation
}

export const CommentItem: React.FC<CommentItemProps> = ({
    comment,
    auth,
    allComments,
    onReplySubmit,
    replyingToId,
    setReplyingToId,
    level = 0 // Default level is 0 (top-level)
}) => {
    const [isLiked, setIsLiked] = useState(false);
    const [likeCount, setLikeCount] = useState(Math.floor(Math.random() * 10));
    const [replyContent, setReplyContent] = useState('');
    const [isSubmittingReply, setIsSubmittingReply] = useState(false);
    // Local state for follow button UI
    const [localFollowingState, setLocalFollowingState] = useState<Set<string>>(new Set());

    const isReplying = replyingToId === comment.id;

    // Initialize local following state
    useEffect(() => {
        if (auth.isAuthenticated && auth.user) {
            setLocalFollowingState(new Set(auth.user.following.map(u => u.id)));
        }
    }, [auth.user, auth.isAuthenticated]);

    // Determine follow status for this comment's author
    const isAuthorSelf = auth.user?.id === comment.author.id;
    const isFollowingAuthor = localFollowingState.has(comment.author.id);

    const handleLikeClick = () => {
        setIsLiked(!isLiked);
        setLikeCount(isLiked ? likeCount - 1 : likeCount + 1);
    };

    const handleReplyClick = () => {
        setReplyingToId(isReplying ? null : comment.id); // Toggle reply state
        setReplyContent(''); // Clear input when toggling
    };

    const handleReplySubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!replyContent.trim() || isSubmittingReply) return;

        setIsSubmittingReply(true);
        // Simulate submission delay
        setTimeout(() => {
            onReplySubmit(replyContent.trim(), comment.id);
            setReplyContent('');
            setReplyingToId(null); // Close reply input after submission
            setIsSubmittingReply(false);
        }, 300);
    };

    // Handler for toggling follow state (simulated)
    const handleFollowToggle = (targetUserId: string) => {
        if (!auth.isAuthenticated) return;

        const isCurrentlyFollowing = localFollowingState.has(targetUserId);
        console.log(`[CommentItem] Attempting to ${isCurrentlyFollowing ? 'unfollow' : 'follow'} user: ${targetUserId}`);

        setLocalFollowingState(prev => {
            const newSet = new Set(prev);
            if (isCurrentlyFollowing) {
                newSet.delete(targetUserId);
            } else {
                newSet.add(targetUserId);
            }
            return newSet;
        });
        // In a real app, call an API here
    };

    // Find direct replies to this comment
    const directReplies = allComments
        .filter(c => c.parentId === comment.id)
        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    return (
        <div className={`py-4 ${level > 0 ? 'ml-6 pl-4 border-l border-gray-200' : 'border-b border-gray-100 last:border-b-0'}`}>
            <div className="flex items-start space-x-3">
                <img
                    src={comment.author.avatar}
                    alt={comment.author.name}
                    className="w-9 h-9 rounded-full bg-gray-100 mt-1 flex-shrink-0"
                />
                <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center space-x-2">
                            <span className="text-sm font-semibold text-gray-800">{comment.author.name}</span>
                            {auth.isAuthenticated && !isAuthorSelf && (
                                <button
                                    onClick={() => handleFollowToggle(comment.author.id)}
                                    className={`px-1.5 py-0 rounded text-xs transition-colors ${isFollowingAuthor
                                        ? 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                                        : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                                        }`}
                                >
                                    {isFollowingAuthor ? '✔' : '+'}
                                </button>
                            )}
                        </div>
                        <span className="text-xs text-gray-400">
                            {formatDistanceToNow(new Date(comment.timestamp), { addSuffix: true, locale: zhCN })}
                        </span>
                    </div>
                    <p className="text-sm text-gray-700 mb-2">{comment.content}</p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center space-x-3">
                            <button
                                onClick={handleLikeClick}
                                className={`flex items-center space-x-1 ${isLiked ? 'text-blue-600' : 'text-gray-500'
                                    } hover:text-blue-600 transition-colors`}
                            >
                                <ThumbsUp className="w-3.5 h-3.5" />
                                <span>{likeCount}</span>
                            </button>
                            {auth.isAuthenticated && (
                                <button
                                    onClick={handleReplyClick}
                                    className="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors"
                                >
                                    <MessageSquare className="w-3.5 h-3.5" />
                                    <span>{isReplying ? '取消回复' : '回复'}</span>
                                </button>
                            )}
                        </div>
                        <button className="text-gray-400 hover:text-gray-600">
                            <MoreHorizontal className="w-4 h-4" />
                        </button>
                    </div>

                    {/* Reply Input Form (Conditionally Rendered) */}
                    {isReplying && auth.isAuthenticated && auth.user && (
                        <form onSubmit={handleReplySubmit} className="flex items-start space-x-3 mt-3">
                            <img
                                src={auth.user.avatar}
                                alt={auth.user.name}
                                className="w-8 h-8 rounded-full bg-gray-100 mt-1 flex-shrink-0"
                            />
                            <div className="flex-1 relative">
                                <textarea
                                    value={replyContent}
                                    onChange={(e) => setReplyContent(e.target.value)}
                                    placeholder={`回复 ${comment.author.name}...`}
                                    rows={2}
                                    className="w-full border border-gray-200 rounded-lg p-2 pr-16 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm"
                                    autoFocus
                                />
                                <button
                                    type="submit"
                                    disabled={!replyContent.trim() || isSubmittingReply}
                                    className="absolute right-2 bottom-2 px-2 py-1 bg-blue-600 text-white rounded-md text-xs hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center"
                                >
                                    <Send className="w-3 h-3 mr-1" />
                                    {isSubmittingReply ? '发布中...' : '发布'}
                                </button>
                            </div>
                        </form>
                    )}

                    {/* Render Direct Replies Recursively */}
                    {directReplies.length > 0 && (
                        <div className="mt-4">
                            {directReplies.map(reply => (
                                <CommentItem
                                    key={reply.id}
                                    comment={reply}
                                    auth={auth}
                                    allComments={allComments}
                                    onReplySubmit={onReplySubmit}
                                    replyingToId={replyingToId}
                                    setReplyingToId={setReplyingToId}
                                    level={level + 1} // Increment level for indentation
                                />
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}; 