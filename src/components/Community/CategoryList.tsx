import React from 'react';
import { POST_CATEGORIES, PostCategory } from '../../types';
import { List } from 'lucide-react';

interface CategoryListProps {
    // Use ReadonlyArray for immutable POST_CATEGORIES
    categories: ReadonlyArray<PostCategory>;
    selectedCategory: PostCategory | 'all';
    onSelectCategory: (category: PostCategory | 'all') => void;
}

export const CategoryList: React.FC<CategoryListProps> = ({
    categories,
    selectedCategory,
    onSelectCategory
}) => {
    return (
        <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <List className="w-5 h-5 mr-2 text-indigo-500" />
                帖子分类
            </h3>
            <ul className="space-y-1.5">
                {/* All Categories Button */}
                <li>
                    <button
                        onClick={() => onSelectCategory('all')}
                        className={`w-full text-left px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${selectedCategory === 'all'
                            ? 'bg-indigo-100 text-indigo-700'
                            : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                            }`}
                    >
                        全部分类
                    </button>
                </li>
                {/* Individual Category Buttons */}
                {categories.map(category => (
                    <li key={category}>
                        <button
                            onClick={() => onSelectCategory(category)}
                            className={`w-full text-left px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${selectedCategory === category
                                ? 'bg-indigo-100 text-indigo-700'
                                : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                                }`}
                        >
                            {category}
                        </button>
                    </li>
                ))}
            </ul>
        </div>
    );
}; 