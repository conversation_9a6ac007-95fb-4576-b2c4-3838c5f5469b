import React from 'react';
import { ProjectDetailData, AuthState } from '../types';
import { Rocket } from 'lucide-react'; // 导入一个图标

interface CreateWorkspaceButtonProps {
    project: ProjectDetailData;
    auth: AuthState;
    onClick: () => void;
}

export const CreateWorkspaceButton: React.FC<CreateWorkspaceButtonProps> = ({ project, auth, onClick }) => {
    // 1. 检查招募是否完成
    const isRecruitmentComplete = project.neededRoles.every(role => role.filled);

    // 2. 检查是否为演示用户 (在当前模拟逻辑中，登录即为演示用户)
    const isDemoUser = !!(auth.isAuthenticated && auth.user);

    // 3. 如果招募未完成且不是演示用户，则不渲染按钮
    if (!isRecruitmentComplete && !isDemoUser) {
        return null;
    }

    // 4. 渲染按钮
    return (
        <button
            onClick={onClick}
            className="mt-4 inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
            <Rocket className="w-4 h-4 mr-2" />
            创建协作空间
        </button>
    );
}; 