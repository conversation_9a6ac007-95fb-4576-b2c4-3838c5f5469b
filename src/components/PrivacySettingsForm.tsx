import React, { useState } from 'react';
import { PrivacySettings, VisibilitySetting } from '../types';
import { Eye, Users, Lock } from 'lucide-react';

interface PrivacySettingsFormProps {
    initialSettings: PrivacySettings;
    onSave: (newSettings: PrivacySettings) => void; // Callback to handle saving
}

const visibilityOptions: { value: VisibilitySetting; label: string; icon: React.FC<{ className?: string }> }[] = [
    { value: 'public', label: '公开可见', icon: Eye },
    { value: 'members_only', label: '仅成员可见', icon: Users },
    { value: 'private', label: '仅自己可见', icon: Lock },
];

export const PrivacySettingsForm: React.FC<PrivacySettingsFormProps> = ({ initialSettings, onSave }) => {
    const [profileVisibility, setProfileVisibility] = useState<VisibilitySetting>(initialSettings.profileVisibility);
    const [projectVisibility, setProjectVisibility] = useState<VisibilitySetting>(initialSettings.projectVisibility);
    const [isSaving, setIsSaving] = useState(false);

    const handleSave = () => {
        setIsSaving(true);
        // Simulate API call
        setTimeout(() => {
            onSave({ profileVisibility, projectVisibility });
            setIsSaving(false);
            alert('隐私设置已保存！'); // Simple feedback
        }, 500);
    };

    const hasChanges =
        profileVisibility !== initialSettings.profileVisibility ||
        projectVisibility !== initialSettings.projectVisibility;

    return (
        <div className="bg-white rounded-2xl shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-5">隐私设置</h2>
            <div className="space-y-5">
                {/* Profile Visibility */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">个人主页可见范围</label>
                    <div className="flex flex-col sm:flex-row gap-2">
                        {visibilityOptions.map(option => (
                            <button
                                key={option.value}
                                onClick={() => setProfileVisibility(option.value)}
                                className={`flex-1 flex items-center justify-center sm:justify-start text-left px-4 py-2 rounded-lg border transition-colors ${profileVisibility === option.value
                                    ? 'bg-blue-50 border-blue-300 text-blue-700'
                                    : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'}`}
                            >
                                <option.icon className="w-4 h-4 mr-2 flex-shrink-0" />
                                <span className="text-sm">{option.label}</span>
                            </button>
                        ))}
                    </div>
                </div>

                {/* Default Project Visibility */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">新项目默认可见范围</label>
                    <div className="flex flex-col sm:flex-row gap-2">
                        {visibilityOptions.map(option => (
                            <button
                                key={option.value}
                                onClick={() => setProjectVisibility(option.value)}
                                className={`flex-1 flex items-center justify-center sm:justify-start text-left px-4 py-2 rounded-lg border transition-colors ${projectVisibility === option.value
                                    ? 'bg-blue-50 border-blue-300 text-blue-700'
                                    : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'}`}
                            >
                                <option.icon className="w-4 h-4 mr-2 flex-shrink-0" />
                                <span className="text-sm">{option.label}</span>
                            </button>
                        ))}
                    </div>
                    <p className="text-xs text-gray-500 mt-1">此设置为您创建新项目时的默认选项。</p>
                </div>
            </div>

            {/* Save Button */}
            <div className="mt-6 text-right">
                <button
                    onClick={handleSave}
                    disabled={!hasChanges || isSaving}
                    className="px-5 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                    {isSaving ? '保存中...' : '保存设置'}
                </button>
            </div>
        </div>
    );
}; 