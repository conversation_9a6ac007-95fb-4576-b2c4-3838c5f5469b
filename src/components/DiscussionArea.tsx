import React, { useState } from 'react';
import { DiscussionComment, AuthState } from '../types';
import { Send } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface DiscussionAreaProps {
    comments: DiscussionComment[];
    auth: AuthState; // 需要知道当前登录用户以显示头像等
    onPostComment: (content: string) => void; // 处理评论提交的回调
}

export const DiscussionArea: React.FC<DiscussionAreaProps> = ({ comments, auth, onPostComment }) => {
    const [newComment, setNewComment] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (newComment.trim() && auth.isAuthenticated) {
            onPostComment(newComment.trim());
            setNewComment('');
        }
    };

    return (
        <div className="bg-white rounded-2xl shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">讨论区</h2>

            {/* 评论列表 */}
            <div className="space-y-4 mb-6 max-h-96 overflow-y-auto pr-2">
                {comments.length > 0 ? (
                    comments.map((comment) => (
                        <div key={comment.id} className="flex items-start space-x-3">
                            <img
                                src={comment.author.avatar}
                                alt={comment.author.name}
                                className="w-8 h-8 rounded-full bg-gray-100 mt-1 flex-shrink-0"
                            />
                            <div className="flex-1 bg-gray-50 rounded-lg p-3">
                                <div className="flex items-center justify-between mb-1">
                                    <span className="text-sm font-semibold text-gray-800">{comment.author.name}</span>
                                    <span className="text-xs text-gray-400">
                                        {formatDistanceToNow(new Date(comment.timestamp), { addSuffix: true, locale: zhCN })}
                                    </span>
                                </div>
                                <p className="text-sm text-gray-700">{comment.content}</p>
                            </div>
                        </div>
                    ))
                ) : (
                    <p className="text-center text-gray-500 py-4">暂无讨论，快来发起话题吧！</p>
                )}
            </div>

            {/* 评论输入框 */}
            {auth.isAuthenticated && auth.user ? (
                <form onSubmit={handleSubmit} className="flex items-start space-x-3">
                    <img
                        src={auth.user.avatar}
                        alt={auth.user.name}
                        className="w-9 h-9 rounded-full bg-gray-100 mt-1 flex-shrink-0"
                    />
                    <div className="flex-1 relative">
                        <textarea
                            value={newComment}
                            onChange={(e) => setNewComment(e.target.value)}
                            placeholder="参与讨论..."
                            rows={2}
                            className="w-full border border-gray-200 rounded-lg p-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                        />
                        <button
                            type="submit"
                            disabled={!newComment.trim()}
                            className="absolute right-2 bottom-2 p-1.5 rounded-md text-blue-600 hover:bg-blue-100 disabled:text-gray-400 disabled:hover:bg-transparent transition-colors"
                            title="发送"
                        >
                            <Send className="w-5 h-5" />
                        </button>
                    </div>
                </form>
            ) : (
                <p className="text-center text-gray-500 text-sm">请 <a href="/login" className="text-blue-600 hover:underline">登录</a> 后参与讨论。</p>
            )}
        </div>
    );
}; 