import React from 'react';
import { ArrowRight } from 'lucide-react';

export const Banner: React.FC = () => {
    return (
        <div className="relative bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl overflow-hidden mb-8">
            <div className="absolute inset-0 bg-black/30"></div>
            {/* 您可以替换为更具吸引力的背景图片 */}
            <img
                src="https://images.unsplash.com/photo-1470225620780-dba8ba36b745?auto=format&fit=crop&w=1920&q=80"
                alt="音乐创作背景"
                className="absolute inset-0 w-full h-full object-cover opacity-50"
            />
            <div className="relative max-w-4xl mx-auto py-20 px-4 sm:px-6 lg:px-8 text-center text-white">
                <h1 className="text-4xl md:text-5xl font-extrabold mb-4 tracking-tight">
                    释放你的音乐才华
                </h1>
                <p className="text-lg md:text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                    在 Ibom 音乐协作平台，与全球音乐人连接、协作，共同创作非凡乐章。
                </p>
                <button className="inline-flex items-center px-8 py-3 bg-white text-blue-600 rounded-lg font-bold text-lg hover:bg-blue-50 transition-colors shadow-lg">
                    开始探索项目
                    <ArrowRight className="w-5 h-5 ml-2" />
                </button>
            </div>
        </div>
    );
}; 