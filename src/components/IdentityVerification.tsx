import React from 'react';
import { ProgressiveIdentityStatus, VerificationStatus } from '../types';
import { ShieldCheck, ShieldAlert, Clock, HelpCircle, CheckCircle, UserCheck, Star } from 'lucide-react';

interface IdentityVerificationProps {
    status: ProgressiveIdentityStatus;
}

const StatusIndicator = ({ status }: { status: VerificationStatus }) => {
    switch (status) {
        case 'verified':
            return <ShieldCheck className="w-5 h-5 text-green-500" />;
        case 'pending':
            return <Clock className="w-5 h-5 text-yellow-500 animate-pulse" />;
        case 'unverified':
            return <ShieldAlert className="w-5 h-5 text-red-500" />;
        default:
            return <HelpCircle className="w-5 h-5 text-gray-400" />;
    }
};

const statusText: Record<VerificationStatus, string> = {
    verified: '已认证',
    pending: '审核中',
    unverified: '未认证',
};

const statusColor: Record<VerificationStatus, string> = {
    verified: 'text-green-600 bg-green-50',
    pending: 'text-yellow-600 bg-yellow-50',
    unverified: 'text-red-600 bg-red-50',
};

export const IdentityVerification: React.FC<IdentityVerificationProps> = ({ status }) => {
    const verificationItems = [
        {
            id: 'realName',
            title: '实名认证',
            description: '保障平台安全与互信的基础。',
            currentStatus: status.realName,
            icon: UserCheck,
            actionText: status.realName === 'unverified' ? '去认证' : undefined,
        },
        {
            id: 'professional',
            title: '专业能力认证',
            description: '上传作品或资历证明，提升信誉。',
            currentStatus: status.professional,
            icon: Star,
            actionText: status.professional === 'unverified' ? '上传证明' : undefined,
        },
        {
            id: 'musician',
            title: '音乐人身份验证',
            description: '关联音乐平台账号，展示专业身份。',
            currentStatus: status.musician,
            icon: CheckCircle,
            actionText: status.musician === 'unverified' ? '关联账号' : undefined,
        },
    ];

    return (
        <div className="bg-white rounded-2xl shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">身份认证</h2>
            <div className="space-y-4">
                {verificationItems.map(item => (
                    <div key={item.id} className="flex items-center justify-between p-4 border border-gray-100 rounded-lg bg-gray-50/50">
                        <div className="flex items-center space-x-3">
                            <div className={`p-2 rounded-full ${statusColor[item.currentStatus]}`}>
                                <item.icon className="w-5 h-5" />
                            </div>

                            <div>
                                <h3 className="font-medium text-gray-800">{item.title}</h3>
                                <p className="text-xs text-gray-500">{item.description}</p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-3">
                            <span className={`text-sm font-medium ${statusColor[item.currentStatus].split(' ')[0]}`}>{statusText[item.currentStatus]}</span>
                            {item.actionText && (
                                <button className="text-xs text-blue-600 hover:underline">
                                    {item.actionText}
                                </button>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}; 