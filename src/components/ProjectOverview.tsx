import React from 'react';
import { ProjectDetailData, UserRole } from '../types';
import { CheckCircle, UserPlus } from 'lucide-react';

interface ProjectOverviewProps {
    project: ProjectDetailData;
}

const roleLabels: Record<UserRole, string> = {
    lyricist: '作词',
    composer: '作曲',
    singer: '演唱',
    arranger: '编曲',
    mixer: '混音',
    producer: '制作',
};

export const ProjectOverview: React.FC<ProjectOverviewProps> = ({ project }) => {
    return (
        <div className="bg-white rounded-2xl shadow-sm p-6 mb-6">
            {/* Header: Title, Creator, Status */}
            <div className="flex flex-col sm:flex-row justify-between items-start mb-4">
                <div>
                    <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-1">{project.name}</h1>
                    <div className="flex items-center text-sm text-gray-500">
                        <img src={project.creator.avatar} alt={project.creator.name} className="w-5 h-5 rounded-full mr-1.5" />
                        <span>由 <span className="font-medium text-gray-700">{project.creator.name}</span> 创建</span>
                        <span className="mx-2">•</span>
                        <span>{new Date(project.createdAt).toLocaleDateString('zh-CN')}</span>
                    </div>
                </div>
                <span
                    className={`mt-2 sm:mt-0 px-3 py-1 text-sm font-medium rounded-full ${project.status === 'completed'
                        ? 'bg-green-100 text-green-700'
                        : 'bg-blue-100 text-blue-700'
                        }`}
                >
                    {project.status === 'completed' ? '已完成' : '进行中'}
                </span>
            </div>

            {/* Description */}
            <p className="text-gray-700 mb-5">{project.description}</p>

            {/* Tags */}
            {project.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-5">
                    {project.tags.map((tag) => (
                        <span key={tag} className="px-2.5 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                            {tag}
                        </span>
                    ))}
                </div>
            )}

            {/* Progress Bar */}
            <div className="mb-5">
                <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">项目进度</span>
                    <span className="text-gray-900 font-medium">{project.progress}%</span>
                </div>
                <div className="w-full h-2.5 bg-gray-200 rounded-full overflow-hidden">
                    <div
                        className="h-full bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full transition-all duration-500 ease-out"
                        style={{ width: `${project.progress}%` }}
                    />
                </div>
            </div>

            {/* Needed Roles */}
            <div>
                <h3 className="text-md font-semibold text-gray-800 mb-3">所需角色：</h3>
                <div className="flex flex-wrap gap-3">
                    {project.neededRoles.map(({ role, filled }) => (
                        <div
                            key={role}
                            className={`flex items-center px-3 py-1.5 rounded-lg border ${filled
                                ? 'bg-green-50 border-green-200 text-green-700'
                                : 'bg-orange-50 border-orange-200 text-orange-700'
                                }`}
                        >
                            {filled ? (
                                <CheckCircle className="w-4 h-4 mr-1.5" />
                            ) : (
                                <UserPlus className="w-4 h-4 mr-1.5" />
                            )}
                            <span className="text-sm font-medium">{roleLabels[role]} {filled ? '(已就位)' : '(招募中)'}</span>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}; 