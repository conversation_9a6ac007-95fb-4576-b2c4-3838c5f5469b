import React, { useState } from 'react';
import { Play, Headphones, Gift, User, Heart, Bookmark } from 'lucide-react';
import { HotSong } from '../types';
import { Link } from 'react-router-dom';
import { PurchaseSuccessModal } from './PurchaseSuccessModal';

interface HotSongsProps {
    hotSongs: HotSong[];
    currentUserPoints: number;
    onPurchaseSong: (songId: string, cost: number) => boolean;
}

export const HotSongs: React.FC<HotSongsProps> = ({ hotSongs, currentUserPoints, onPurchaseSong }) => {
    const formatPlays = (num: number): string => {
        if (num >= 10000) {
            return (num / 10000).toFixed(1) + '万';
        }
        return num.toLocaleString();
    };

    const songCost = 50;
    const [purchaseSuccessInfo, setPurchaseSuccessInfo] = useState<{ songId: string; songTitle: string } | null>(null);

    const handlePurchaseAttempt = (songId: string, songTitle: string) => {
        if (currentUserPoints < songCost) {
            alert(`积分不足！购买需要 ${songCost} 积分。`);
            return;
        }

        const success = onPurchaseSong(songId, songCost);

        if (success) {
            setPurchaseSuccessInfo({ songId, songTitle });
            console.log(`Purchase confirmed for song: ${songId}`);
            setTimeout(() => {
                setPurchaseSuccessInfo(null);
            }, 10000);
        } else {
            console.log(`Purchase failed for song: ${songId}`);
        }
    };

    // State for Likes
    const [likeCounts, setLikeCounts] = useState<Record<string, number>>(() =>
        hotSongs.reduce((acc, song) => {
            acc[song.id] = song.likeCount || 0; // Use likeCount from data
            return acc;
        }, {} as Record<string, number>)
    );
    const [likedSongs, setLikedSongs] = useState<Set<string>>(new Set());

    // State for Collections
    const [collectedSongs, setCollectedSongs] = useState<Set<string>>(new Set());

    const handleLike = (songId: string) => {
        const isLiked = likedSongs.has(songId);
        const currentCount = likeCounts[songId] || 0;

        setLikeCounts(prev => ({
            ...prev,
            [songId]: isLiked ? currentCount - 1 : currentCount + 1
        }));

        setLikedSongs(prev => {
            const newSet = new Set(prev);
            if (isLiked) {
                newSet.delete(songId);
            } else {
                newSet.add(songId);
            }
            return newSet;
        });
        console.log(`Toggled like for song: ${songId}. New status: ${!isLiked}`);
    };

    const handleCollect = (songId: string) => {
        const isCollected = collectedSongs.has(songId);
        setCollectedSongs(prev => {
            const newSet = new Set(prev);
            if (isCollected) {
                newSet.delete(songId);
            } else {
                newSet.add(songId);
            }
            return newSet;
        });
        console.log(`Toggled collection for song: ${songId}. New status: ${!isCollected}`);
    };

    return (
        <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-5 flex items-center">
                <Headphones className="w-6 h-6 mr-2 text-purple-500" />
                热门歌曲 (协作)
            </h2>
            {hotSongs.length === 0 ? (
                <div className="text-center py-10 text-gray-500">
                    暂无热门协作歌曲。
                </div>
            ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    {hotSongs.map((song) => {
                        // Get current like/collect status and like count
                        const isLiked = likedSongs.has(song.id);
                        const isCollected = collectedSongs.has(song.id);
                        const currentLikeCount = likeCounts[song.id] || 0;
                        return (
                            <div key={song.id} className="group bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 flex flex-col">
                                <div className="relative">
                                    <img src={song.coverArt} alt={song.title} className="w-full h-40 object-cover" />
                                    <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                                        <button className="p-3 bg-white/80 rounded-full text-blue-600 hover:bg-white transition-colors scale-90 group-hover:scale-100">
                                            <Play className="w-6 h-6 fill-current" />
                                        </button>
                                    </div>
                                </div>
                                <div className="p-4 flex-grow flex flex-col justify-between">
                                    <div>
                                        <h3 className="font-semibold text-gray-800 truncate mb-1">{song.title}</h3>
                                        <p className="text-sm text-gray-500 truncate mb-2" title={song.artists.join(', ')}>艺术家: {song.artists.join(', ')}</p>

                                        <div className="mb-3 flex items-center space-x-2 text-xs text-gray-600">
                                            <User className="w-3.5 h-3.5 flex-shrink-0 text-gray-400" />
                                            <span>创作者:</span>
                                            <div className="flex items-center space-x-1 overflow-hidden">
                                                {song.creators.slice(0, 3).map(creator => (
                                                    <Link key={creator.id} to={`/profile/${creator.id}`} title={creator.name} className="flex items-center space-x-1 hover:underline">
                                                        <img src={creator.avatar} alt={creator.name} className="w-4 h-4 rounded-full border border-gray-200" />
                                                    </Link>
                                                ))}
                                                {song.creators.length > 3 && <span className="text-xs text-gray-400">+ {song.creators.length - 3}</span>}
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex justify-between items-center text-xs text-gray-400 mt-2 pt-2 border-t border-gray-100">
                                        <span className="px-1.5 py-0.5 bg-gray-100 text-gray-600 rounded text-xs">{song.genre}</span>
                                        <div className="flex items-center space-x-3">
                                            <span className="flex items-center text-gray-400" title={`${song.plays.toLocaleString()} 次播放`}>
                                                <Headphones className="w-4 h-4 mr-1" />
                                                {formatPlays(song.plays)}
                                            </span>
                                            <button
                                                onClick={() => handleLike(song.id)}
                                                title={isLiked ? "取消点赞" : "点赞"}
                                                className={`flex items-center transition-colors duration-200 ${isLiked ? 'text-red-500 hover:text-red-600' : 'text-gray-400 hover:text-red-500'}`}
                                            >
                                                <Heart className={`w-4 h-4 mr-1 ${isLiked ? 'fill-current' : ''}`} />
                                                <span>{currentLikeCount.toLocaleString()}</span>
                                            </button>
                                            <button
                                                onClick={() => handleCollect(song.id)}
                                                title={isCollected ? "取消收藏" : "收藏"}
                                                className={`flex items-center transition-colors duration-200 ${isCollected ? 'text-yellow-500 hover:text-yellow-600' : 'text-gray-400 hover:text-yellow-500'}`}
                                            >
                                                <Bookmark className={`w-4 h-4 ${isCollected ? 'fill-current' : ''}`} />
                                            </button>
                                            <button
                                                onClick={() => handlePurchaseAttempt(song.id, song.title)}
                                                title="购买支持创作者 (50 积分)"
                                                disabled={currentUserPoints < songCost}
                                                className={`flex items-center transition-colors duration-200 font-medium ${currentUserPoints < songCost ? 'text-gray-400 cursor-not-allowed' : 'text-green-600 hover:text-green-700'}`}
                                            >
                                                <Gift className={`w-4 h-4 mr-1`} />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            )}
            <PurchaseSuccessModal
                isOpen={!!purchaseSuccessInfo}
                onClose={() => setPurchaseSuccessInfo(null)}
                songTitle={purchaseSuccessInfo?.songTitle || ''}
            />
        </div>
    );
}; 