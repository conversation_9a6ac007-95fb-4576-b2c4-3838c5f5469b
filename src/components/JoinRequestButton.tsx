import React from 'react';
import { ProjectDetailData, AuthState, UserRole } from '../types';
import { UserPlus, Check } from 'lucide-react';

interface JoinRequestButtonProps {
    project: ProjectDetailData;
    auth: AuthState;
}

const roleLabels: Record<UserRole, string> = {
    lyricist: '作词',
    composer: '作曲',
    singer: '演唱',
    arranger: '编曲',
    mixer: '混音',
    producer: '制作',
};

export const JoinRequestButton: React.FC<JoinRequestButtonProps> = ({ project, auth }) => {
    if (!auth.isAuthenticated || !auth.user) {
        return (
            <button
                onClick={() => window.location.href = '/login'} // 简单跳转
                className="w-full mt-6 px-6 py-3 bg-gray-200 text-gray-600 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
            >
                请先登录以申请加入
            </button>
        );
    }

    // 模拟检查用户是否已加入
    const isMember = project.members.some(member => member.id === auth.user?.id);
    if (isMember) {
        return (
            <div className="w-full mt-6 px-6 py-3 bg-green-100 text-green-700 rounded-lg font-semibold text-center flex items-center justify-center">
                <Check className="w-5 h-5 mr-2" /> 您已是项目成员
            </div>
        );
    }

    // 检查用户是否拥有项目所需的、且未被填补的角色
    const canApplyForRoles = project.neededRoles
        .filter(needed => !needed.filled && auth.user?.roles.includes(needed.role));

    if (canApplyForRoles.length === 0) {
        return (
            <div className="w-full mt-6 px-6 py-3 bg-yellow-100 text-yellow-700 rounded-lg font-semibold text-center">
                暂无可申请的角色或您不符合要求
            </div>
        );
    }

    const handleApply = () => {
        // TODO: 实现申请逻辑 (e.g., call API)
        alert(`已申请加入项目 ${project.name}，申请的角色：${canApplyForRoles.map(r => roleLabels[r.role]).join(', ')}`);
    };

    return (
        <button
            onClick={handleApply}
            className="w-full mt-6 px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center"
        >
            <UserPlus className="w-5 h-5 mr-2" />
            申请加入 (可申请：{canApplyForRoles.map(r => roleLabels[r.role]).join(' / ')})
        </button>
    );
}; 