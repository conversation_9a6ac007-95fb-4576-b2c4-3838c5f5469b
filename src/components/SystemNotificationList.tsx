import React from 'react';
import { Link } from 'react-router-dom';
import { SystemNotification } from '../types';
import { BellR<PERSON>, CheckCheck } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface SystemNotificationListProps {
    notifications: SystemNotification[];
    onMarkAsRead?: (id: string) => void; // Optional callback to handle marking as read
    onMarkAllAsRead?: () => void;      // Optional callback for all read
}

export const SystemNotificationList: React.FC<SystemNotificationListProps> = ({
    notifications,
    onMarkAsRead,
    onMarkAllAsRead
}) => {

    const unreadCount = notifications.filter(n => !n.isRead).length;

    return (
        <div className="h-full flex flex-col">
            <div className="flex justify-between items-center p-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-800">系统通知</h2>
                {unreadCount > 0 && onMarkAllAsRead && (
                    <button
                        onClick={onMarkAllAsRead}
                        className="text-xs text-blue-600 hover:underline font-medium"
                    >
                        全部标记已读 ({unreadCount})
                    </button>
                )}
            </div>

            {notifications.length === 0 ? (
                <div className="flex-grow flex items-center justify-center text-gray-500">
                    暂无系统通知
                </div>
            ) : (
                <ul className="flex-grow overflow-y-auto p-2 space-y-2">
                    {notifications.map((notification) => (
                        <li key={notification.id} className={`p-3 rounded-lg transition-colors ${notification.isRead ? 'bg-white' : 'bg-blue-50/70 hover:bg-blue-100'}`}>
                            <div className="flex items-start space-x-3">
                                <div className={`mt-1 flex-shrink-0 p-1.5 rounded-full ${notification.isRead ? 'bg-gray-100 text-gray-400' : 'bg-blue-100 text-blue-600'}`}>
                                    <BellRing className="w-4 h-4" />
                                </div>
                                <div className="flex-1 min-w-0">
                                    <div className="flex justify-between items-center mb-0.5">
                                        <span className={`text-sm font-semibold ${notification.isRead ? 'text-gray-700' : 'text-gray-900'}`}>{notification.title}</span>
                                        <span className="text-xs text-gray-400 flex-shrink-0 ml-2">
                                            {formatDistanceToNow(new Date(notification.timestamp), { addSuffix: true, locale: zhCN })}
                                        </span>
                                    </div>
                                    <p className={`text-sm ${notification.isRead ? 'text-gray-500' : 'text-gray-700'} mb-1 line-clamp-2`}>
                                        {notification.content}
                                    </p>
                                    <div className="flex items-center justify-between text-xs">
                                        {notification.link ? (
                                            <Link to={notification.link} className="text-blue-600 hover:underline">查看详情</Link>
                                        ) : <span></span>}
                                        {!notification.isRead && onMarkAsRead && (
                                            <button
                                                onClick={() => onMarkAsRead(notification.id)}
                                                className="text-gray-400 hover:text-green-600 flex items-center"
                                                title="标记已读"
                                            >
                                                <CheckCheck className="w-3.5 h-3.5 mr-1" /> 标记已读
                                            </button>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
}; 