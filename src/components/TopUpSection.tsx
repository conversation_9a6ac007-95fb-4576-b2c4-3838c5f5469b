import React, { useState } from 'react';
import { Coins, CheckCircle } from 'lucide-react';

interface TopUpSectionProps {
    currentPoints: number;
    onTopUp: (amountInYuan: number) => boolean; // Returns true on success for feedback
}

const predefinedAmounts = [10, 50, 100, 200]; // Example amounts in CNY

export const TopUpSection: React.FC<TopUpSectionProps> = ({ currentPoints, onTopUp }) => {
    const [selectedAmount, setSelectedAmount] = useState<number | null>(null);
    const [customAmount, setCustomAmount] = useState<string>('');
    const [feedbackMessage, setFeedbackMessage] = useState<string | null>(null);
    const [isProcessing, setIsProcessing] = useState(false);

    const handleAmountSelect = (amount: number) => {
        setSelectedAmount(amount);
        setCustomAmount(''); // Clear custom amount if predefined is selected
    };

    const handleCustomAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        // Allow only numbers and optionally a decimal point
        if (/^\d*\.?\d*$/.test(value)) {
            setCustomAmount(value);
            const numValue = parseFloat(value);
            setSelectedAmount(isNaN(numValue) || numValue <= 0 ? null : numValue);
        }
    };

    const handleConfirmTopUp = () => {
        if (selectedAmount === null || selectedAmount <= 0 || isProcessing) {
            return; // No valid amount selected or already processing
        }

        setIsProcessing(true);
        setFeedbackMessage(null);

        // Simulate API call / processing delay before calling onTopUp
        setTimeout(() => {
            const success = onTopUp(selectedAmount);
            if (success) {
                setFeedbackMessage(`成功充值 ${selectedAmount * 10} 积分!`);
                // Clear selection after successful top-up
                setSelectedAmount(null);
                setCustomAmount('');
            } else {
                setFeedbackMessage('充值失败，请稍后再试。');
            }

            setIsProcessing(false);

            // Clear feedback message after a delay
            setTimeout(() => {
                setFeedbackMessage(null);
            }, 3000);

        }, 500); // Simulate 0.5 second delay
    };

    return (
        <div className="space-y-6 p-4">
            {/* Current Balance */}
            <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">我的积分</h3>
                <div className="flex items-center space-x-2 text-3xl font-bold text-yellow-600">
                    <Coins className="w-7 h-7" />
                    <span>{currentPoints.toLocaleString()}</span>
                </div>
            </div>

            {/* Top-up Section */}
            <div>
                <h4 className="text-md font-semibold text-gray-700 mb-1">账户充值</h4>
                <p className="text-sm text-gray-500 mb-4">充值汇率：1 元 = 10 积分</p>

                {/* Predefined Amounts */}
                <div className="flex flex-wrap gap-3 mb-4">
                    {predefinedAmounts.map(amount => (
                        <button
                            key={amount}
                            onClick={() => handleAmountSelect(amount)}
                            disabled={isProcessing}
                            className={`px-4 py-2 rounded-lg border text-sm font-medium transition-colors ${selectedAmount === amount
                                ? 'bg-blue-500 text-white border-blue-500'
                                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 disabled:opacity-50'
                                }`}
                        >
                            {amount} 元
                        </button>
                    ))}
                </div>

                {/* Custom Amount (Optional) */}
                <div className="mb-4">
                    <label htmlFor="customAmount" className="block text-sm font-medium text-gray-700 mb-1">或输入自定义金额 (元):</label>
                    <input
                        type="text" // Use text to allow decimal input easily
                        id="customAmount"
                        value={customAmount}
                        onChange={handleCustomAmountChange}
                        disabled={isProcessing}
                        placeholder="例如 12.5"
                        className="w-full sm:w-48 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                        inputMode="decimal"
                    />
                </div>

                {/* Confirm Button and Feedback */}
                <div className="flex items-center gap-4">
                    <button
                        onClick={handleConfirmTopUp}
                        disabled={selectedAmount === null || selectedAmount <= 0 || isProcessing}
                        className="px-6 py-2 rounded-lg bg-green-600 text-white font-medium hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isProcessing ? '处理中...' : '确认充值'}
                    </button>
                    {feedbackMessage && (
                        <div className="flex items-center text-green-600 text-sm font-medium animate-fade-in">
                            <CheckCircle className="w-4 h-4 mr-1" />
                            {feedbackMessage}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

// Add basic fade-in animation to global CSS or tailwind.config.js if desired
/* 
@keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
.animate-fade-in { animation: fadeIn 0.5s ease-out forwards; }
*/ 