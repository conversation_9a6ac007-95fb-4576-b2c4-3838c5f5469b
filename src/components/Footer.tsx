import React from 'react';

export const Footer: React.FC = () => {
    const currentYear = new Date().getFullYear();

    return (
        <footer className="bg-gray-100 border-t border-gray-200 mt-12 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center md:text-left">
                    {/* Logo & Description */}
                    <div>
                        <h2 className="text-xl font-bold text-blue-600 mb-2">Ibom</h2>
                        <p className="text-gray-600 text-sm">
                            连接全球音乐创作者的协作平台。
                        </p>
                    </div>

                    {/* Links */}
                    <div>
                        <h3 className="text-lg font-semibold text-gray-800 mb-3">快速链接</h3>
                        <ul className="space-y-2 text-sm">
                            <li><a href="/about" className="text-gray-600 hover:text-blue-600 transition-colors">关于我们</a></li>
                            <li><a href="/terms" className="text-gray-600 hover:text-blue-600 transition-colors">服务条款</a></li>
                            <li><a href="/privacy" className="text-gray-600 hover:text-blue-600 transition-colors">隐私政策</a></li>
                            <li><a href="/contact" className="text-gray-600 hover:text-blue-600 transition-colors">联系我们</a></li>
                        </ul>
                    </div>

                    {/* Social & Contact */}
                    <div>
                        <h3 className="text-lg font-semibold text-gray-800 mb-3">保持联系</h3>
                        <p className="text-gray-600 text-sm mb-3">
                            关注我们的社交媒体，获取最新动态。
                        </p>
                        {/* 在这里添加社交媒体图标链接 */}
                        <div className="flex justify-center md:justify-start space-x-4 mt-4">
                            {/* Example: <a href="#" className="text-gray-500 hover:text-blue-600">...</a> */}
                        </div>
                    </div>
                </div>

                {/* Copyright */}
                <div className="mt-8 pt-8 border-t border-gray-200 text-center text-sm text-gray-500">
                    &copy; {currentYear} Ibom. 保留所有权利。
                </div>
            </div>
        </footer>
    );
}; 