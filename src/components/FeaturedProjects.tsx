import React from 'react';
import { Flame } from 'lucide-react';
import { Project } from '../types';
import { ProjectCard } from './ProjectCard'; // 假设您有一个项目卡片组件

// 模拟热门项目数据
const featuredProjectsData: Project[] = [
    {
        id: 'f1',
        name: '赛博朋克主题配乐征集',
        description: '为一款独立游戏创作具有未来感的赛博朋克配乐',
        progress: 40,
        status: 'ongoing',
        members: [
            { id: 'm1', name: '艾丽丝', avatar: '/avatars/5.jpg' },
            { id: 'm2', name: '鲍勃', avatar: '/avatars/6.jpg' },
            { id: 'm3', name: '查理', avatar: '/avatars/7.jpg' },
        ],
        createdAt: '2024-04-05T11:00:00Z',
        updatedAt: '2024-04-09T09:15:00Z',
        tags: ['赛博朋克', '游戏配乐', '电子'],
    },
    {
        id: 'f2',
        name: '夏日流行歌曲创作营',
        description: '共同创作一首充满活力的夏日流行歌曲，目标是登上排行榜',
        progress: 85,
        status: 'ongoing',
        members: [
            { id: 'm4', name: '戴夫', avatar: '/avatars/8.jpg' },
            { id: 'm5', name: '伊芙', avatar: '/avatars/9.jpg' },
        ],
        createdAt: '2024-03-20T14:00:00Z',
        updatedAt: '2024-04-07T18:00:00Z',
        tags: ['流行', '夏日', '排行榜', '团队协作'],
    },
    {
        id: 'f3',
        name: '独立电影纪录片配乐',
        description: '为一个关于环境保护的纪录片创作感人肺腑的配乐',
        progress: 60,
        status: 'ongoing',
        members: [
            { id: 'm6', name: '弗兰克', avatar: '/avatars/10.jpg' },
        ],
        createdAt: '2024-04-01T09:30:00Z',
        updatedAt: '2024-04-08T12:45:00Z',
        tags: ['电影配乐', '纪录片', '环境', '管弦乐'],
    },
];

export const FeaturedProjects: React.FC = () => {
    return (
        <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-5 flex items-center">
                <Flame className="w-6 h-6 mr-2 text-red-500" />
                热门项目推荐
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredProjectsData.map((project) => (
                    <ProjectCard key={project.id} project={project} />
                ))}
            </div>
        </div>
    );
}; 