import React, { useState, useEffect, useRef } from 'react';
import { ChatConversation, ChatMessage, User } from '../types';
import { Send, ArrowLeft } from 'lucide-react';

interface ChatWindowProps {
    conversation: ChatConversation | null;
    messages: ChatMessage[];
    currentUser: User | null;
    onSendMessage: (conversationId: string, content: string) => void;
    onBack?: () => void; // For mobile view back button
}

export const ChatWindow: React.FC<ChatWindowProps> = ({
    conversation,
    messages,
    currentUser,
    onSendMessage,
    onBack
}) => {
    const [newMessage, setNewMessage] = useState('');
    const messagesEndRef = useRef<HTMLDivElement>(null);

    const otherParticipant = conversation?.participants.find(p => p.id !== currentUser?.id);

    useEffect(() => {
        // Scroll to bottom when messages change
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, [messages]);

    const handleSend = (e?: React.FormEvent) => {
        e?.preventDefault();
        if (newMessage.trim() && conversation && currentUser) {
            onSendMessage(conversation.id, newMessage.trim());
            setNewMessage('');
        }
    };

    if (!conversation || !currentUser) {
        return (
            <div className="h-full flex flex-col items-center justify-center text-gray-500 bg-gray-50">
                选择一个会话开始聊天
            </div>
        );
    }

    return (
        <div className="h-full flex flex-col bg-white">
            {/* Header */}
            <div className="flex items-center p-3 border-b border-gray-200">
                {onBack && (
                    <button onClick={onBack} className="mr-2 p-1 rounded-full hover:bg-gray-100 md:hidden">
                        <ArrowLeft className="w-5 h-5 text-gray-600" />
                    </button>
                )}
                {otherParticipant && (
                    <>
                        <img src={otherParticipant.avatar} alt={otherParticipant.name} className="w-8 h-8 rounded-full mr-2" />
                        <span className="font-semibold text-gray-800">{otherParticipant.name}</span>
                    </>
                )}
            </div>

            {/* Messages Area */}
            <div className="flex-grow overflow-y-auto p-4 space-y-4 bg-gray-50">
                {messages.map((msg) => (
                    <div key={msg.id} className={`flex ${msg.senderId === currentUser.id ? 'justify-end' : 'justify-start'}`}>
                        <div className={`flex items-end max-w-[75%] ${msg.senderId === currentUser.id ? 'flex-row-reverse' : 'flex-row'} space-x-2 space-x-reverse`}>
                            {msg.senderId !== currentUser.id && (
                                <img src={msg.senderAvatar} alt={msg.senderName} className="w-6 h-6 rounded-full flex-shrink-0 self-start" />
                            )}
                            <div
                                className={`px-3 py-2 rounded-lg ${msg.senderId === currentUser.id ? 'bg-blue-500 text-white' : 'bg-white text-gray-800 shadow-sm'}`}
                            >
                                {/* Can add sender name for group chats later */}
                                <p className="text-sm break-words">{msg.content}</p>
                                {/* Can add timestamp on hover/click later */}
                            </div>
                        </div>
                    </div>
                ))}
                <div ref={messagesEndRef} />
            </div>

            {/* Input Area */}
            <div className="p-3 border-t border-gray-200 bg-white">
                <form onSubmit={handleSend} className="flex items-center space-x-2">
                    <input
                        type="text"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        placeholder="输入消息..."
                        className="flex-1 border border-gray-200 rounded-full px-4 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                    />
                    <button
                        type="submit"
                        disabled={!newMessage.trim()}
                        className="p-2 rounded-full bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-300 transition-colors"
                        title="发送"
                    >
                        <Send className="w-5 h-5" />
                    </button>
                </form>
            </div>
        </div>
    );
}; 