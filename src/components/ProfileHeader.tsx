import React from 'react';
import { UserProfile, UserRole, VerificationStatus } from '../types';
import { Edit, Briefcase, Coins, BadgeCheck, Clock, Award } from 'lucide-react';

interface ProfileHeaderProps {
    userProfile: UserProfile;
    currentPoints: number;
    musicianStatus: VerificationStatus;
}

const roleDisplayNames: Record<UserRole, string> = {
    lyricist: '作词',
    composer: '作曲',
    singer: '演唱',
    arranger: '编曲',
    mixer: '混音',
    producer: '制作人',
};

// Level Calculation Function
const calculateLevel = (points: number): number => {
    if (points >= 300) return 3;
    if (points >= 100) return 2;
    return 1;
};

export const ProfileHeader: React.FC<ProfileHeaderProps> = ({ userProfile, currentPoints, musicianStatus }) => {
    // Calculate level
    const level = calculateLevel(currentPoints);
    const levelColorClasses = [ // Define colors per level
        'bg-gray-100 text-gray-600', // Level 1
        'bg-green-100 text-green-700', // Level 2
        'bg-yellow-100 text-yellow-800' // Level 3
    ];

    return (
        <div className="bg-white rounded-2xl shadow-sm p-6 mb-6 flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6">
            {/* Avatar */}
            <img
                src={userProfile.avatar}
                alt={userProfile.name}
                className="w-24 h-24 rounded-full border-4 border-blue-100 shadow-md"
            />
            {/* Info */}
            <div className="flex-1 text-center sm:text-left">
                <div className="flex items-center justify-center sm:justify-start mb-1">
                    <h1 className="text-2xl font-bold text-gray-900 mr-2">{userProfile.name}</h1>
                    <span
                        className={`flex items-center px-2 py-0.5 rounded-full text-xs font-bold ${levelColorClasses[level - 1]} mr-2`}
                        title={`等级 ${level}`}
                    >
                        <Award className="w-3 h-3 mr-1" />
                        Lv.{level}
                    </span>
                    {musicianStatus === 'verified' && (
                        <span title="已认证音乐人">
                            <BadgeCheck className="w-5 h-5 text-blue-500 mr-1.5 flex-shrink-0" />
                        </span>
                    )}
                    {musicianStatus === 'pending' && (
                        <span title="音乐人身份审核中">
                            <Clock className="w-5 h-5 text-yellow-500 mr-1.5 flex-shrink-0" />
                        </span>
                    )}
                    <button className="text-gray-400 hover:text-blue-600 p-1 rounded-full hover:bg-blue-50 transition-colors ml-auto sm:ml-2">
                        <Edit className="w-4 h-4" />
                    </button>
                </div>
                <p className="text-sm text-gray-500 mb-3">{userProfile.email}</p>
                <div className="mb-3 flex justify-center sm:justify-start items-center space-x-2 text-yellow-600">
                    <Coins className="w-5 h-5" />
                    <span className="font-semibold text-base">{currentPoints.toLocaleString()} 积分</span>
                </div>
                {userProfile.roles && userProfile.roles.length > 0 && (
                    <div className="mb-3 flex flex-wrap justify-center sm:justify-start items-center gap-2">
                        <Briefcase className="w-4 h-4 text-gray-500 flex-shrink-0" />
                        {userProfile.roles.map(role => (
                            <span key={role} className="px-2.5 py-1 text-xs bg-purple-50 text-purple-700 rounded-full font-medium">
                                {roleDisplayNames[role] || role}
                            </span>
                        ))}
                    </div>
                )}
                {userProfile.bio && (
                    <p className="text-gray-700 mb-3 text-sm leading-relaxed">{userProfile.bio}</p>
                )}
                {userProfile.skills && userProfile.skills.length > 0 && (
                    <div className="mb-3">
                        <h4 className="text-xs text-gray-500 font-semibold mb-1">技能:</h4>
                        <div className="flex flex-wrap justify-center sm:justify-start gap-2">
                            {userProfile.skills.map(skill => (
                                <span key={skill} className="px-2.5 py-1 text-xs bg-blue-50 text-blue-700 rounded-full font-medium">
                                    {skill}
                                </span>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}; 