import React from 'react';
import { <PERSON><PERSON><PERSON>, Users, TrendingUp } from 'lucide-react';

// 模拟数据
const genreTrends = [
    { name: '流行 Pop', value: 35, color: 'bg-blue-500' },
    { name: '摇滚 Rock', value: 25, color: 'bg-red-500' },
    { name: '嘻哈 HipHop', value: 18, color: 'bg-yellow-500' },
    { name: '电子 Electronic', value: 12, color: 'bg-purple-500' },
    { name: '民谣 Folk', value: 10, color: 'bg-green-500' },
];

const topContributors = [
    { id: 'u1', name: '节奏大师', avatar: 'https://api.dicebear.com/7.x/pixel-art/svg?seed=Leo', contributions: 15 },
    { id: 'u2', name: '旋律诗人', avatar: 'https://api.dicebear.com/7.x/adventurer/svg?seed=Max', contributions: 12 },
    { id: 'u3', name: '和声女王', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Luna', contributions: 10 },
    { id: 'u4', name: '编曲奇才', avatar: 'https://api.dicebear.com/7.x/bottts/svg?seed=Charlie', contributions: 8 },
    { id: 'u5', name: '混音魔法师', avatar: 'https://api.dicebear.com/7.x/micah/svg?seed=Milo', contributions: 7 },
    { id: 'u6', name: '作词小能手', avatar: 'https://api.dicebear.com/7.x/lorelei/svg?seed=Coco', contributions: 5 },
];

export const GlobalTrends: React.FC = () => {
    return (
        <div className="bg-white rounded-2xl shadow-sm p-6 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <BarChart className="w-6 h-6 mr-2 text-green-500" />
                全球趋势
            </h2>

            <div className="grid grid-cols-1 gap-8">
                {/* 音乐风格趋势 */}
                <div>
                    <h3 className="text-lg font-semibold mb-4 flex items-center">
                        <TrendingUp className="w-5 h-5 mr-2 text-indigo-500" />
                        热门音乐风格
                    </h3>
                    <div className="space-y-3">
                        {genreTrends.map((genre) => (
                            <div key={genre.name} className="flex items-center">
                                <span className="w-28 text-sm text-gray-600 truncate">{genre.name}</span>
                                <div className="flex-1 h-3 bg-gray-100 rounded-full overflow-hidden mx-2">
                                    <div
                                        className={`h-full ${genre.color} rounded-full`}
                                        style={{ width: `${genre.value}%` }}
                                    />
                                </div>
                                <span className="text-sm font-medium text-gray-800">{genre.value}%</span>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
}; 