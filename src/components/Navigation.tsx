import React, { useState, useEffect, useRef } from 'react';
import { NavLink, Link } from 'react-router-dom';
import { Home, Users, FolderKanban, MessageSquare, User, LogIn, LayoutGrid, LogOut, Activity, Bookmark, PenSquare } from 'lucide-react';
import { NavItem, AuthState } from '../types';

// 基础导航项 - 对所有用户可见
const baseNavItems: NavItem[] = [
  { label: '首页', path: '/', icon: Home },
  { label: '协作广场', path: '/collaboration', icon: Users },
  { label: '社群互动', path: '/community', icon: LayoutGrid },
];

// 需要登录的导航项
const authNavItems: NavItem[] = [
  { label: '我的项目', path: '/projects', icon: FolderKanban },
  { label: '消息中心', path: '/messages', icon: MessageSquare },
];

// 不再需要 unauthNavItems

interface NavigationProps {
  auth: AuthState;
  onLogout: () => void;
}

export const Navigation: React.FC<NavigationProps> = ({ auth, onLogout }) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [unreadCount, setUnreadCount] = useState<number>(105);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogoutClick = () => {
    setIsDropdownOpen(false);
    onLogout();
  };

  const navItems = auth.isAuthenticated ? [...baseNavItems, ...authNavItems] : baseNavItems;

  return (
    <>
      {/* Desktop Navigation */}
      <nav className={`hidden md:block fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? 'bg-white/95 shadow-md backdrop-blur-sm' : 'bg-white'
        }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo and Brand */}
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-blue-600">Ibom</h1>
              <p className="ml-2 text-sm text-gray-500">音乐协作平台</p>
            </div>

            {/* Navigation Items & User Info/Auth Buttons Container */}
            <div className="flex items-center">
              {/* Navigation Items */}
              <div className="flex space-x-1 mr-6">
                {navItems.map((item) => {
                  const IconComponent = item.icon as React.ElementType;
                  const isMessageCenter = item.label === '消息中心'; // Check if it's the message center item

                  return (
                    <NavLink
                      key={item.path}
                      to={item.path}
                      className={({ isActive }) =>
                        `flex items-center px-4 py-2 rounded-lg transition-all duration-200
                        hover:bg-gray-50 hover:scale-105
                        ${isActive
                          ? 'bg-blue-50 text-blue-600'
                          : 'text-gray-600 hover:text-gray-900'
                        }`
                      }
                    >
                      {/* Wrap icon and label in a relative container */}
                      <span className="relative inline-flex items-center">
                        <IconComponent className="w-5 h-5 mr-2" />
                        <span>{item.label}</span>

                        {/* Conditional Badge */}
                        {isMessageCenter && unreadCount > 0 && (
                          <span
                            className="absolute -top-1 -right-1 transform translate-x-1/2 -translate-y-1/2 bg-red-500 text-white text-xs font-bold rounded-full px-1.5 h-[18px] min-w-[18px] flex items-center justify-center leading-none shadow"
                            style={{ fontSize: '0.65rem', lineHeight: '1' }} // Fine-tune size
                          >
                            {unreadCount > 99 ? '99+' : unreadCount}
                          </span>
                        )}
                      </span>
                    </NavLink>
                  );
                })}
              </div>

              {/* Conditional Rendering: User Dropdown or Login/Register Buttons */}
              {auth.isAuthenticated && auth.user ? (
                <div className="relative" ref={dropdownRef}>
                  <button
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    className="flex items-center p-1 rounded-full hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <img
                      src={auth.user.avatar}
                      alt={auth.user.name}
                      className="w-8 h-8 rounded-full"
                    />
                    <span className="ml-2 text-sm font-medium text-gray-700 hidden lg:block">{auth.user.name}</span>
                  </button>

                  {isDropdownOpen && (
                    <div className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                      <Link
                        to="/profile"
                        onClick={() => setIsDropdownOpen(false)}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                      >
                        <User className="w-4 h-4 mr-2" />
                        个人中心
                      </Link>
                      <Link
                        to="/profile/activity"
                        onClick={() => setIsDropdownOpen(false)}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                      >
                        <Activity className="w-4 h-4 mr-2" />
                        动态
                      </Link>
                      <Link
                        to="/profile/collections"
                        onClick={() => setIsDropdownOpen(false)}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                      >
                        <Bookmark className="w-4 h-4 mr-2" />
                        收藏
                      </Link>
                      <Link
                        to="/creator-center"
                        onClick={() => setIsDropdownOpen(false)}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                      >
                        <PenSquare className="w-4 h-4 mr-2" />
                        创作中心
                      </Link>
                      <button
                        onClick={handleLogoutClick}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                      >
                        <LogOut className="w-4 h-4 mr-2" />
                        登出
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Link
                    to="/login"
                    className="px-4 py-1.5 text-sm font-medium text-blue-600 rounded-md hover:bg-blue-50 transition-colors"
                  >
                    登录
                  </Link>
                  <Link
                    to="/register"
                    className="px-4 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
                  >
                    注册
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
        <div className="flex justify-around">
          {navItems.map((item) => {
            const IconComponent = item.icon as React.ElementType;
            const isMessageCenter = item.label === '消息中心'; // Check if it's the message center item

            return (
              <NavLink
                key={item.path}
                to={item.path}
                className={({ isActive }) =>
                  `flex flex-col items-center justify-center flex-1 py-2 px-1 transition-colors text-center ${isActive ? 'text-blue-600' : 'text-gray-600 hover:text-gray-900'}`
                }
              >
                {/* Wrap icon and label in a relative container */}
                <span className="relative inline-flex flex-col items-center">
                  <IconComponent className="w-6 h-6 mb-0.5" />
                  <span className="text-[10px] leading-tight">{item.label}</span>

                  {/* Conditional Badge for mobile */}
                  {isMessageCenter && unreadCount > 0 && (
                    <span
                      className="absolute top-0 right-0 transform translate-x-1/3 -translate-y-1/3 bg-red-500 text-white text-[10px] font-bold rounded-full px-1 h-[14px] min-w-[14px] flex items-center justify-center leading-none shadow"
                      style={{ fontSize: '0.6rem', lineHeight: '1' }} // Fine-tune size for mobile
                    >
                      {unreadCount > 99 ? '99+' : unreadCount}
                    </span>
                  )}
                </span>
              </NavLink>
            );
          })}
          {!auth.isAuthenticated && (
            <Link to="/login" className="flex flex-col items-center justify-center flex-1 py-2 px-1 text-gray-600 hover:text-gray-900 text-center">
              <LogIn className="w-6 h-6 mb-0.5" />
              <span className="text-[10px] leading-tight">登录</span>
            </Link>
          )}
          {auth.isAuthenticated && (
            <Link to="/profile" className="flex flex-col items-center justify-center flex-1 py-2 px-1 text-gray-600 hover:text-gray-900 text-center">
              <User className="w-6 h-6 mb-0.5" />
              <span className="text-[10px] leading-tight">我的</span>
            </Link>
          )}
        </div>
      </nav>
    </>
  );
};