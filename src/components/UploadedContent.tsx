import React from 'react';
import { UploadedFile } from '../types';
import { getFileTypeIcon } from '../utils/fileIcons';
import { Download } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface UploadedContentProps {
    files: UploadedFile[];
}

export const UploadedContent: React.FC<UploadedContentProps> = ({ files }) => {
    if (!files || files.length === 0) {
        return (
            <div className="bg-white rounded-2xl shadow-sm p-6 mb-6 text-center">
                <h2 className="text-xl font-semibold text-gray-900 mb-3">已上传内容</h2>
                <p className="text-gray-500">暂无上传的文件。</p>
            </div>
        );
    }

    return (
        <div className="bg-white rounded-2xl shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">已上传内容</h2>
            <ul className="space-y-3">
                {files.map((file) => {
                    const IconComponent = getFileTypeIcon(file.type);
                    const uploadedDate = new Date(file.uploadedAt);
                    const uploadedTimeAgo = !isNaN(uploadedDate.getTime())
                        ? formatDistanceToNow(uploadedDate, { addSuffix: true, locale: zhCN })
                        : '无效日期';

                    return (
                        <li key={file.id} className="flex items-center justify-between border-b border-gray-100 pb-3 last:border-b-0 last:pb-0">
                            <div className="flex items-center space-x-3 flex-1 min-w-0">
                                <IconComponent className="w-5 h-5 text-gray-500 flex-shrink-0" />
                                <div className="min-w-0">
                                    <p className="text-sm font-medium text-gray-800 truncate">{file.name}</p>
                                    <p className="text-xs text-gray-500">
                                        由 {file.uploader.name} 上传于 {uploadedTimeAgo}
                                    </p>
                                </div>
                            </div>
                            <a
                                href={file.fileUrl}
                                download={file.name}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="ml-4 flex-shrink-0 p-1.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                                title={`下载 ${file.name}`}
                            >
                                <Download className="w-4 h-4" />
                            </a>
                        </li>
                    );
                })}
            </ul>
        </div>
    );
}; 