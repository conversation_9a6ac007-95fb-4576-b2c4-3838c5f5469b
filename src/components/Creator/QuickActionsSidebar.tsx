import React from 'react';
import { Link } from 'react-router-dom';
import { Plus } from 'lucide-react';

// Import HotActivities
import { HotActivities } from '../Community/HotActivities'; // Adjust path as needed
// Import Revenue Overview
import { RevenueOverviewSidebar } from './RevenueOverviewSidebar';

export const QuickActionsSidebar: React.FC = () => {
    return (
        <div className="space-y-6">
            <div className="bg-white p-4 rounded-lg shadow-sm space-y-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">快速操作</h3>
                <Link
                    to="/projects" // Link to the Projects page
                    className="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm font-medium shadow-sm"
                >
                    <Plus className="w-5 h-5 mr-2" />
                    创建新项目
                </Link>
                {/* Placeholder for other quick actions */}
                {/* 
      <button className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 bg-white rounded-md hover:bg-gray-50 transition-colors text-sm font-medium">
        邀请协作者 (占位)
      </button> 
      */}
            </div>

            {/* Revenue Overview Section */}
            <RevenueOverviewSidebar />

            {/* Hot Activities Section */}
            <HotActivities />
        </div>
    );
}; 