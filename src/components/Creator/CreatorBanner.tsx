import React from 'react';

export const CreatorBanner: React.FC = () => {
    // Placeholder image URL - replace with a relevant one
    const bannerImageUrl = 'https://images.unsplash.com/photo-1507878866276-a9477f60d7d9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80';

    return (
        <div
            className="relative bg-gray-800 text-white rounded-lg overflow-hidden shadow-lg mb-8 h-48 md:h-56 flex items-center justify-center"
            style={{
                backgroundImage: `url(${bannerImageUrl})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center'
            }}
        >
            {/* Overlay to darken the image slightly for better text contrast */}
            <div className="absolute inset-0 bg-black opacity-50"></div>

            {/* Text Content */}
            <div className="relative z-10 text-center px-4">
                <h2 className="text-3xl md:text-4xl font-bold mb-2">
                    欢迎来到创作中心
                </h2>
                <p className="text-base md:text-lg opacity-90">
                    管理您的项目，追踪数据，释放创造力。
                </p>
            </div>
        </div>
    );
}; 