import React from 'react';

interface DataOverviewCardProps {
    icon: React.ElementType;
    title: string;
    value: string | number;
}

export const DataOverviewCard: React.FC<DataOverviewCardProps> = ({ icon: Icon, title, value }) => {
    return (
        <div className="bg-white p-4 rounded-lg shadow-sm flex items-center space-x-4">
            <div className="p-2 bg-blue-100 rounded-full">
                <Icon className="w-6 h-6 text-blue-600" />
            </div>
            <div>
                <p className="text-sm text-gray-500 font-medium">{title}</p>
                <p className="text-xl font-semibold text-gray-900">{value}</p>
            </div>
        </div>
    );
}; 