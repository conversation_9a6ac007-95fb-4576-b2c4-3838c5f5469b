import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRight, Wallet } from 'lucide-react'; // Added Wallet icon

export const RevenueOverviewSidebar: React.FC = () => {
    // Mock data - replace with actual data fetching later
    const mockRevenue = 158.75;

    return (
        <div className="bg-white p-4 rounded-lg shadow-sm">
            <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2 flex items-center">
                <Wallet className="w-5 h-5 mr-2 text-blue-600" />
                收益概览
            </h3>
            <div className="text-center mt-4 mb-2">
                <p className="text-sm text-gray-500 mb-1">
                    预估收益 (元)
                </p>
                <p className="text-3xl font-bold text-blue-600">
                    {mockRevenue.toFixed(2)}
                </p>
            </div>
            <Link
                to="/revenue-details" // Placeholder link
                className="mt-4 flex items-center justify-center text-sm text-blue-600 hover:text-blue-800 transition-colors"
            >
                查看详情
                <ChevronRight className="w-4 h-4 ml-1" />
            </Link>
        </div>
    );
}; 