import React, { useState } from 'react';
import { Music, Mic, Pen, Sliders, Headphones, Star, Mail, Lock, ArrowRight } from 'lucide-react';
import { UserRole } from '../types';

const roles: { id: UserRole; label: string; icon: React.ComponentType; description: string }[] = [
  { id: 'lyricist', label: '作词人', icon: Pen, description: '创作歌词，讲述故事' },
  { id: 'composer', label: '作曲人', icon: Music, description: '谱写旋律，创造音乐' },
  { id: 'singer', label: '歌手', icon: Mic, description: '用声音诠释作品' },
  { id: 'arranger', label: '编曲', icon: Sliders, description: '丰富音乐层次' },
  { id: 'mixer', label: '混音', icon: Headphones, description: '平衡音频效果' },
  { id: 'producer', label: '制作人', icon: Star, description: '把控作品方向' },
];

interface Props {
  onComplete: (roles: UserRole[]) => void;
}

export const Registration: React.FC<Props> = ({ onComplete }) => {
  const [step, setStep] = useState<'register' | 'selectRoles'>('register');
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('');
  const [emailError, setEmailError] = useState('');
  const [selectedRoles, setSelectedRoles] = useState<UserRole[]>([]);

  const validateEmail = (email: string): boolean => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };

  const handleRegisterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateEmail(email)) {
      setEmailError('请输入有效的邮箱地址');
      return;
    }
    console.log('模拟注册:', { email, password });
    setEmailError('');
    setStep('selectRoles');
  };

  const toggleRole = (role: UserRole) => {
    setSelectedRoles(prev =>
      prev.includes(role)
        ? prev.filter(r => r !== role)
        : [...prev, role]
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 flex items-center justify-center p-6">
      <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow-xl p-8 md:p-12 w-full">
        {step === 'register' && (
          <form onSubmit={handleRegisterSubmit} className="max-w-md mx-auto">
            <h1 className="text-3xl font-bold text-center mb-4 text-gray-800">欢迎加入 Ibom</h1>
            <p className="text-center text-gray-600 mb-8">只需一步，开启你的音乐协作之旅</p>

            <div className="mb-4 relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="email"
                placeholder="请输入邮箱地址"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  if (emailError) setEmailError('');
                }}
                required
                className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent ${emailError ? 'border-red-500 ring-red-500' : 'border-gray-200 focus:ring-blue-500'}`}
              />
              {emailError && <p className="text-red-500 text-xs mt-1">{emailError}</p>}
            </div>

            <div className="mb-6 relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="password"
                placeholder="设置密码 (至少6位)"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                minLength={6}
                required
                className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <button
              type="submit"
              className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors disabled:bg-gray-400"
              disabled={!email || password.length < 6}
            >
              下一步：选择角色 <ArrowRight className="w-5 h-5 ml-2" />
            </button>
          </form>
        )}

        {step === 'selectRoles' && (
          <div>
            <h1 className="text-3xl font-bold text-center mb-4 text-gray-800">选择你的音乐角色</h1>
            <p className="text-center text-gray-600 mb-10">你可以选择多个角色，这有助于其他成员了解你的技能。</p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {roles.map(({ id, label, icon: Icon, description }) => {
                const IconComponent = Icon as React.ElementType;
                return (
                  <button
                    key={id}
                    onClick={() => toggleRole(id)}
                    className={`p-6 rounded-xl text-center transition-all transform hover:scale-105
                      ${selectedRoles.includes(id)
                        ? 'bg-blue-600 text-white shadow-lg ring-2 ring-blue-300'
                        : 'bg-white text-gray-800 shadow hover:shadow-md border border-gray-200'
                      }`}
                  >
                    <IconComponent className="w-8 h-8 mb-4 mx-auto" />
                    <h3 className="text-xl font-semibold mb-2">{label}</h3>
                    <p className={`text-sm ${selectedRoles.includes(id) ? 'text-blue-100' : 'text-gray-500'}`}>
                      {description}
                    </p>
                  </button>
                );
              })}
            </div>

            <div className="mt-12 text-center">
              <button
                onClick={() => onComplete(selectedRoles)}
                disabled={selectedRoles.length === 0}
                className={`px-8 py-3 rounded-full text-lg font-medium transition-colors
                  ${selectedRoles.length > 0
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                  }`}
              >
                完成注册并进入平台
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};