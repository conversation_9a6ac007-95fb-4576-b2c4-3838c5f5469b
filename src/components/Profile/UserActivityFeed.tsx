import React from 'react';
import { UserActivity } from '../../types';
import { MessageSquare, Heart, Gift, UserPlus, Clock } from 'lucide-react';
import { Link } from 'react-router-dom';

interface UserActivityFeedProps {
    activities: UserActivity[];
}

// Helper to format time difference (simplified)
const formatTimeAgo = (timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const secondsPast = (now.getTime() - date.getTime()) / 1000;

    if (secondsPast < 60) {
        return `${Math.round(secondsPast)}秒前`;
    }
    if (secondsPast < 3600) {
        return `${Math.round(secondsPast / 60)}分钟前`;
    }
    if (secondsPast <= 86400) { // Within a day
        return `${Math.round(secondsPast / 3600)}小时前`;
    }
    // Older than a day - show date
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
};

// Helper to get icon for activity type
const getActivityIcon = (type: UserActivity['type']) => {
    switch (type) {
        case 'new_post': return <MessageSquare className="w-4 h-4 text-blue-500" />;
        case 'liked_song': return <Heart className="w-4 h-4 text-red-500" />;
        case 'purchased_song': return <Gift className="w-4 h-4 text-green-500" />;
        case 'followed_user': return <UserPlus className="w-4 h-4 text-purple-500" />;
        default: return <Clock className="w-4 h-4 text-gray-400" />;
    }
}

export const UserActivityFeed: React.FC<UserActivityFeedProps> = ({ activities }) => {
    if (!activities || activities.length === 0) {
        return (
            <div className="text-center py-10 text-gray-500">
                暂无动态。
            </div>
        );
    }

    return (
        <ul className="space-y-5">
            {activities.map((activity, index) => (
                <li key={index} className="flex items-start space-x-3 pb-3 border-b border-gray-100 last:border-b-0">
                    <div className="flex-shrink-0 mt-1">
                        {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-grow text-sm">
                        <p className="text-gray-700 leading-snug">
                            {/* Conditional text based on type */}
                            {activity.type === 'new_post' && (
                                <>发布了帖子 <Link to={`/posts/${activity.postId}`} className="font-medium text-blue-600 hover:underline">{activity.postTitle}</Link></>
                            )}
                            {activity.type === 'liked_song' && (
                                <>点赞了歌曲 <Link to={`/songs/${activity.songId}`} className="font-medium text-blue-600 hover:underline">{activity.songTitle}</Link></>
                            )}
                            {activity.type === 'purchased_song' && (
                                <>购买支持了歌曲 <Link to={`/songs/${activity.songId}`} className="font-medium text-blue-600 hover:underline">{activity.songTitle}</Link></>
                            )}
                            {activity.type === 'followed_user' && (
                                <>关注了用户 <Link to={`/profile/${activity.userId}`} className="font-medium text-blue-600 hover:underline">{activity.userName}</Link></>
                            )}
                        </p>
                        <p className="text-xs text-gray-400 mt-1">
                            {formatTimeAgo(activity.timestamp)}
                        </p>
                    </div>
                </li>
            ))}
        </ul>
    );
}; 