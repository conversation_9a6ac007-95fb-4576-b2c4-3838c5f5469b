import React from 'react';
import { Link } from 'react-router-dom';
import { Music } from 'lucide-react';
import { CreatedSong } from '../../types';

interface UserCreatedSongsListProps {
    songs?: CreatedSong[];
}

export const UserCreatedSongsList: React.FC<UserCreatedSongsListProps> = ({ songs }) => {
    if (!songs || songs.length === 0) {
        return (
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 text-center text-gray-500">
                暂无创作歌曲。
            </div>
        );
    }

    return (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
            <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <Music className="w-5 h-5 mr-2 text-purple-600" />
                创作的歌曲
            </h3>
            <div className="space-y-4">
                {songs.map(song => (
                    <Link
                        key={song.id}
                        to={song.link}
                        className="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors border border-transparent hover:border-gray-200 group"
                    >
                        <img
                            src={song.coverArt}
                            alt={song.title}
                            className="w-12 h-12 rounded object-cover mr-4 flex-shrink-0 shadow-sm"
                        />
                        <div className="flex-grow min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate group-hover:text-blue-600 transition-colors">
                                {song.title}
                            </p>
                            <p className="text-xs text-gray-500 truncate">
                                艺术家: {song.artists.join(', ')}
                            </p>
                        </div>
                        {/* Optional: Add play button or other actions here */}
                    </Link>
                ))}
            </div>
        </div>
    );
}; 