import React from 'react';
import { ChatConversation, User } from '../types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface ConversationListProps {
    conversations: ChatConversation[];
    currentUser: User | null; // Need current user to identify the other participant
    selectedConversationId: string | null;
    onSelectConversation: (conversationId: string) => void;
}

export const ConversationList: React.FC<ConversationListProps> = ({
    conversations,
    currentUser,
    selectedConversationId,
    onSelectConversation
}) => {

    const getOtherParticipant = (conv: ChatConversation): Pick<User, 'id' | 'name' | 'avatar'> | null => {
        if (!currentUser) return null;
        return conv.participants.find(p => p.id !== currentUser.id) || null;
    };

    return (
        <div className="h-full flex flex-col">
            <div className="p-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-800">私信列表</h2>
                {/* Add search input if needed */}
            </div>
            {conversations.length === 0 ? (
                <div className="flex-grow flex items-center justify-center text-gray-500">
                    暂无私信
                </div>
            ) : (
                <ul className="flex-grow overflow-y-auto">
                    {conversations.map((conv) => {
                        const otherUser = getOtherParticipant(conv);
                        if (!otherUser) return null; // Should not happen in practice

                        const isSelected = conv.id === selectedConversationId;
                        const lastMsgText = conv.lastMessage
                            ? `${conv.lastMessage.senderName === currentUser?.name ? '你: ' : ''}${conv.lastMessage.content}`
                            : '暂无消息';

                        return (
                            <li key={conv.id}>
                                <button
                                    onClick={() => onSelectConversation(conv.id)}
                                    className={`w-full flex items-center space-x-3 p-3 text-left transition-colors ${isSelected ? 'bg-blue-50' : 'hover:bg-gray-50'}`}
                                >
                                    <img
                                        src={otherUser.avatar}
                                        alt={otherUser.name}
                                        className="w-10 h-10 rounded-full flex-shrink-0 bg-gray-100"
                                    />
                                    <div className="flex-1 min-w-0">
                                        <div className="flex justify-between items-center mb-0.5">
                                            <span className={`text-sm font-semibold truncate ${conv.unreadCount > 0 ? 'text-gray-900' : 'text-gray-700'}`}>{otherUser.name}</span>
                                            {conv.lastMessage && (
                                                <span className="text-xs text-gray-400 flex-shrink-0 ml-2">
                                                    {formatDistanceToNow(new Date(conv.lastMessage.timestamp), { addSuffix: true, locale: zhCN })}
                                                </span>
                                            )}
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <p className={`text-xs truncate ${conv.unreadCount > 0 ? 'text-gray-600 font-medium' : 'text-gray-500'}`}>
                                                {lastMsgText}
                                            </p>
                                            {conv.unreadCount > 0 && (
                                                <span className="ml-2 flex-shrink-0 text-xs font-bold text-white bg-red-500 rounded-full px-1.5 py-0.5 leading-none">
                                                    {conv.unreadCount}
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                </button>
                            </li>
                        );
                    })}
                </ul>
            )
            }
        </div>
    );
}; 