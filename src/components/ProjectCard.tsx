import React from 'react';
import { Project } from '../types';
import { Clock, Users } from 'lucide-react';

interface ProjectCardProps {
    project: Project;
    onClick?: () => void;
}

export const ProjectCard: React.FC<ProjectCardProps> = ({ project, onClick }) => {
    return (
        <div
            onClick={onClick}
            className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer p-4 border border-gray-100"
        >
            {/* Project Header */}
            <div className="flex justify-between items-start mb-3">
                <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {project.name}
                    </h3>
                    <p className="text-sm text-gray-500 line-clamp-2">
                        {project.description}
                    </p>
                </div>
                <span
                    className={`px-2 py-1 text-xs rounded-full ${project.status === 'completed'
                            ? 'bg-green-100 text-green-700'
                            : 'bg-blue-100 text-blue-700'
                        }`}
                >
                    {project.status === 'completed' ? '已完成' : '进行中'}
                </span>
            </div>

            {/* Progress Bar */}
            <div className="mb-4">
                <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">项目进度</span>
                    <span className="text-gray-900 font-medium">{project.progress}%</span>
                </div>
                <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
                    <div
                        className="h-full bg-blue-500 rounded-full transition-all duration-300"
                        style={{ width: `${project.progress}%` }}
                    />
                </div>
            </div>

            {/* Project Info */}
            <div className="flex items-center justify-between text-sm text-gray-500">
                <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4" />
                    <span>{project.members.length} 位成员</span>
                </div>
                <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4" />
                    <span>
                        {new Date(project.updatedAt).toLocaleDateString('zh-CN', {
                            month: 'short',
                            day: 'numeric'
                        })}
                    </span>
                </div>
            </div>

            {/* Tags */}
            {project.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-3">
                    {project.tags.map((tag) => (
                        <span
                            key={tag}
                            className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full"
                        >
                            {tag}
                        </span>
                    ))}
                </div>
            )}
        </div>
    );
}; 